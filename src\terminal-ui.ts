import blessed from 'blessed';
import { MasterAI } from './master-ai';
import { Logger } from './logger';
import { UIState } from './types';

export class TerminalUI {
  private screen!: blessed.Widgets.Screen;
  private masterAI: MasterAI;
  private logger: Logger;
  private state: UIState;
  private boxes: { [key: string]: any } = {};

  constructor(masterAI: MasterAI, logger: Logger) {
    this.masterAI = masterAI;
    this.logger = logger;
    this.state = {
      currentView: 'main',
      showDebug: false
    };
    
    this.initializeScreen();
    this.setupLayout();
    this.setupEventHandlers();
  }

  private initializeScreen(): void {
    this.screen = blessed.screen({
      smartCSR: true,
      title: 'Terminal AI Orchestrator'
    });

    // ESC tuşu ile çıkış
    this.screen.key(['escape', 'q', 'C-c'], () => {
      return process.exit(0);
    });
  }

  private setupLayout(): void {
    // Ana container - Modern dark theme
    const container = blessed.box({
      parent: this.screen,
      width: '100%',
      height: '100%',
      style: {
        bg: '#0d1117' // GitHub dark theme background
      }
    });

    // Modern header with gradient effect
    const header = blessed.box({
      parent: container,
      top: 0,
      left: 0,
      width: '100%',
      height: 4,
      content: '{center}{bold}🚀 Terminal AI Orchestrator v2.0{/bold}{/center}\n{center}{blue-fg}Advanced Multi-AI Coordination System{/}{/center}',
      tags: true,
      border: {
        type: 'line'
      },
      style: {
        bg: 'black',
        fg: 'white',
        bold: true,
        border: {
          fg: 'blue'
        }
      }
    });

    // Sol panel - System Dashboard (Modern design)
    const leftPanel = blessed.box({
      parent: container,
      label: ' 📊 System Dashboard ',
      top: 4,
      left: 0,
      width: '35%',
      height: '65%',
      border: {
        type: 'line'
      },
      style: {
        bg: 'black',
        fg: 'white',
        border: {
          fg: 'green'
        },
        label: {
          fg: 'green',
          bold: true
        }
      },
      scrollable: true,
      alwaysScroll: true,
      mouse: true
    });

    this.boxes.leftPanel = leftPanel;

    // Orta panel - Conversation Hub (Modern chat design)
    const chatPanel = blessed.box({
      parent: container,
      label: ' 💬 AI Conversation Hub ',
      top: 4,
      left: '35%',
      width: '45%',
      height: '65%',
      border: {
        type: 'line'
      },
      style: {
        bg: 'black',
        fg: 'white',
        border: {
          fg: 'cyan'
        },
        label: {
          fg: 'cyan',
          bold: true
        }
      },
      scrollable: true,
      alwaysScroll: true,
      mouse: true
    });

    this.boxes.chatPanel = chatPanel;

    // Sağ panel - Activity Monitor (Modern log design)
    const logPanel = blessed.box({
      parent: container,
      label: ' 📋 Activity Monitor ',
      top: 4,
      left: '80%',
      width: '20%',
      height: '65%',
      border: {
        type: 'line'
      },
      style: {
        bg: 'black',
        fg: 'white',
        border: {
          fg: 'yellow'
        },
        label: {
          fg: 'yellow',
          bold: true
        }
      },
      scrollable: true,
      alwaysScroll: true,
      mouse: true
    });

    this.boxes.logPanel = logPanel;

    // Alt panel - Smart Input (Modern input design)
    const inputPanel = blessed.textbox({
      parent: container,
      label: ' ✨ Smart AI Input (Enter: Send, Tab: Autocomplete, Esc: Exit) ',
      bottom: 1,
      left: 0,
      width: '100%',
      height: 4,
      border: {
        type: 'line'
      },
      style: {
        bg: 'black',
        fg: 'white',
        border: {
          fg: 'magenta'
        },
        label: {
          fg: 'magenta',
          bold: true
        },
        focus: {
          bg: 'black',
          fg: 'white',
          border: {
            fg: 'red'
          }
        }
      },
      inputOnFocus: true,
      mouse: true
    });

    this.boxes.inputPanel = inputPanel;

    // Modern status bar with icons
    const statusBar = blessed.box({
      parent: container,
      bottom: 0,
      left: 0,
      width: '100%',
      height: 1,
      content: '{center}🟢 Ready | {bold}F1{/bold}:Help | {bold}F2{/bold}:Status | {bold}F3{/bold}:Debug | {bold}F4{/bold}:Performance | {bold}F5{/bold}:Stats | {bold}ESC{/bold}:Exit{/center}',
      tags: true,
      style: {
        bg: 'blue',
        fg: 'white',
        bold: true
      }
    });

    this.boxes.statusBar = statusBar;

    // Performance indicator (top-right corner)
    const perfIndicator = blessed.box({
      parent: container,
      top: 0,
      right: 0,
      width: 25,
      height: 3,
      content: '{center}⚡ Performance{/center}\n{center}🟢 Optimal{/center}',
      tags: true,
      border: {
        type: 'line'
      },
      style: {
        bg: 'black',
        fg: 'green',
        border: {
          fg: 'green'
        }
      }
    });

    this.boxes.perfIndicator = perfIndicator;
  }

  private setupEventHandlers(): void {
    // Input handling
    this.boxes.inputPanel.on('submit', async (value: string) => {
      if (value.trim()) {
        await this.handleUserInput(value.trim());
        (this.boxes.inputPanel as any).clearValue();
        this.boxes.inputPanel.focus();
      }
    });

    // Klavye kısayolları
    this.screen.key(['f1'], () => {
      this.showHelp();
    });

    this.screen.key(['f2'], () => {
      this.updateWorkerStatus();
    });

    this.screen.key(['f3'], () => {
      this.state.showDebug = !this.state.showDebug;
      this.updateLogPanel();
    });

    this.screen.key(['f4'], () => {
      this.showPerformanceReport();
    });

    this.screen.key(['f5'], () => {
      this.showSystemStatistics();
    });
  }

  private async handleUserInput(input: string): Promise<void> {
    // Kullanıcı mesajını chat paneline ekle
    this.addToChatPanel(`Siz: ${input}`, 'cyan');
    
    try {
      // Master AI'dan yanıt al
      const response = await this.masterAI.processUserInput(input);
      this.addToChatPanel(`Master AI: ${response}`, 'green');
      
      // Worker durumlarını güncelle
      this.updateWorkerStatus();
      
    } catch (error) {
      this.addToChatPanel(`Hata: ${error}`, 'red');
    }
    
    this.screen.render();
  }

  private addToChatPanel(message: string, color: string = 'white'): void {
    const timestamp = new Date().toLocaleTimeString();
    const messageIcon = this.getMessageIcon(color);

    // Modern chat bubble design
    let formattedMessage = '';
    if (color === 'cyan') { // User message
      formattedMessage = `\n{cyan-fg}╭─ 👤 You {gray-fg}${timestamp}{/}\n`;
      formattedMessage += `{cyan-fg}│{/} ${message}\n`;
      formattedMessage += `{cyan-fg}╰─────────────────────────{/}`;
    } else if (color === 'green') { // AI response
      formattedMessage = `\n{green-fg}╭─ 🤖 Master AI {gray-fg}${timestamp}{/}\n`;
      formattedMessage += `{green-fg}│{/} ${message}\n`;
      formattedMessage += `{green-fg}╰─────────────────────────{/}`;
    } else if (color === 'blue') { // System message
      formattedMessage = `\n{blue-fg}╭─ ⚙️  System {gray-fg}${timestamp}{/}\n`;
      formattedMessage += `{blue-fg}│{/} ${message}\n`;
      formattedMessage += `{blue-fg}╰─────────────────────────{/}`;
    } else { // Other messages
      formattedMessage = `\n{${color}-fg}╭─ ${messageIcon} {gray-fg}${timestamp}{/}\n`;
      formattedMessage += `{${color}-fg}│{/} ${message}\n`;
      formattedMessage += `{${color}-fg}╰─────────────────────────{/}`;
    }

    const currentContent = this.boxes.chatPanel.getContent();
    const newContent = currentContent + formattedMessage + '\n';

    this.boxes.chatPanel.setContent(newContent);
    this.boxes.chatPanel.scrollTo(this.boxes.chatPanel.getScrollHeight());
  }

  private getMessageIcon(color: string): string {
    switch (color) {
      case 'cyan': return '👤';
      case 'green': return '🤖';
      case 'blue': return '⚙️';
      case 'red': return '❌';
      case 'yellow': return '⚠️';
      case 'magenta': return '🔮';
      default: return '💬';
    }
  }

  private updateWorkerStatus(): void {
    const workers = this.masterAI.getWorkers();
    const systemStats = this.masterAI.getSystemStatistics();
    let content = '';

    // Modern system overview with visual indicators
    content += `{bold}{cyan-fg}╭─── SYSTEM HEALTH ───╮{/}\n`;
    const healthStatus = this.getSystemHealthStatus(systemStats);
    content += `{bold}Status: ${healthStatus.icon} ${healthStatus.text}{/}\n`;
    content += `{cyan-fg}├─ Workers: {/}{bold}${systemStats.performance.activeWorkers}{/} {green-fg}online{/}\n`;
    content += `{cyan-fg}├─ Response: {/}${this.formatResponseTime(systemStats.performance.averageSystemResponseTime)}\n`;
    content += `{cyan-fg}├─ Throughput: {/}{bold}${systemStats.performance.taskThroughput.toFixed(1)}{/} tasks/h\n`;
    content += `{cyan-fg}╰─ Error Rate: {/}${this.formatErrorRate(systemStats.performance.errorRate)}\n\n`;

    // Modern worker cards
    content += `{bold}{green-fg}╭─── AI WORKERS ───╮{/}\n`;
    workers.forEach((worker, index) => {
      const isLast = index === workers.length - 1;
      const prefix = isLast ? '╰─' : '├─';
      const status = worker.isAvailable() ? '🟢' : '🟡';
      const name = worker.getName();
      const spec = worker.getSpecialization();

      content += `{green-fg}${prefix} {/}${status} {bold}${name}{/}\n`;
      content += `{green-fg}${isLast ? '  ' : '│ '} {/}{gray-fg}${spec} • ${worker.getStatus()}{/}\n`;
      if (!isLast) content += `{green-fg}│{/}\n`;
    });
    content += '\n';

    // Modern task overview with progress bars
    const activeTasks = this.masterAI.getActiveTasks();
    content += `{bold}{yellow-fg}╭─── ACTIVE TASKS ───╮{/}\n`;
    content += `{yellow-fg}├─ Queue: {/}{bold}${activeTasks.length}{/} active\n`;
    content += `{yellow-fg}├─ Total: {/}{bold}${systemStats.taskOrchestrator.taskHistory}{/} processed\n`;

    if (activeTasks.length > 0) {
      content += `{yellow-fg}├─ Recent:{/}\n`;
      activeTasks.slice(0, 3).forEach((task, index) => {
        const isLast = index === Math.min(activeTasks.length, 3) - 1;
        const prefix = isLast ? '╰─' : '├─';
        const statusIcon = this.getTaskStatusIcon(task.status);
        const priority = task.priority ? `[${task.priority.toUpperCase()}]` : '';

        content += `{yellow-fg}${prefix} {/}${statusIcon} {gray-fg}${priority}{/} ${task.description.substring(0, 20)}...\n`;
      });

      if (activeTasks.length > 3) {
        content += `{gray-fg}   ... and ${activeTasks.length - 3} more{/}\n`;
      }
    } else {
      content += `{yellow-fg}╰─ {/}{gray-fg}No active tasks{/}\n`;
    }

    this.boxes.leftPanel.setContent(content);

    // Update performance indicator
    this.updatePerformanceIndicator(systemStats);
  }

  private getSystemHealthStatus(stats: any): { icon: string, text: string } {
    const errorRate = stats.performance.errorRate;
    const responseTime = stats.performance.averageSystemResponseTime;

    if (errorRate < 0.05 && responseTime < 5000) {
      return { icon: '🟢', text: '{green-fg}Excellent{/}' };
    } else if (errorRate < 0.15 && responseTime < 10000) {
      return { icon: '🟡', text: '{yellow-fg}Good{/}' };
    } else {
      return { icon: '🔴', text: '{red-fg}Needs Attention{/}' };
    }
  }

  private formatResponseTime(ms: number): string {
    if (ms < 1000) {
      return `{green-fg}${ms.toFixed(0)}ms{/}`;
    } else if (ms < 5000) {
      return `{yellow-fg}${(ms/1000).toFixed(1)}s{/}`;
    } else {
      return `{red-fg}${(ms/1000).toFixed(1)}s{/}`;
    }
  }

  private formatErrorRate(rate: number): string {
    const percentage = (rate * 100).toFixed(1);
    if (rate < 0.05) {
      return `{green-fg}${percentage}%{/}`;
    } else if (rate < 0.15) {
      return `{yellow-fg}${percentage}%{/}`;
    } else {
      return `{red-fg}${percentage}%{/}`;
    }
  }

  private getTaskStatusIcon(status: string): string {
    switch (status) {
      case 'completed': return '✅';
      case 'failed': return '❌';
      case 'in_progress': return '⏳';
      case 'pending': return '⏸️';
      default: return '❓';
    }
  }

  private updatePerformanceIndicator(stats: any): void {
    const health = this.getSystemHealthStatus(stats);
    const content = `{center}⚡ Performance{/center}\n{center}${health.icon} ${health.text}{/center}`;
    this.boxes.perfIndicator.setContent(content);
  }

  private updateLogPanel(): void {
    const logs = this.logger.getLogs(undefined, undefined, 15);
    let content = '';

    // Modern log header
    content += `{bold}{yellow-fg}╭─── ACTIVITY LOG ───╮{/}\n`;

    logs.forEach((log, index) => {
      if (!this.state.showDebug && log.level === 'debug') return;

      const isLast = index === logs.length - 1;
      const prefix = isLast ? '╰─' : '├─';
      const levelIcon = this.getLogLevelIcon(log.level);
      const levelColor = this.getLogLevelColor(log.level);

      const time = log.timestamp.toLocaleTimeString();
      const shortMessage = log.message.length > 25 ? log.message.substring(0, 25) + '...' : log.message;

      content += `{yellow-fg}${prefix} {/}${levelIcon} {${levelColor}-fg}${log.level.toUpperCase()}{/}\n`;
      content += `{yellow-fg}${isLast ? '  ' : '│ '} {/}{gray-fg}${time} • ${log.source}{/}\n`;
      content += `{yellow-fg}${isLast ? '  ' : '│ '} {/}${shortMessage}\n`;
      if (!isLast) content += `{yellow-fg}│{/}\n`;
    });

    if (logs.length === 0) {
      content += `{yellow-fg}╰─ {/}{gray-fg}No recent activity{/}\n`;
    }

    this.boxes.logPanel.setContent(content);
    this.boxes.logPanel.scrollTo(this.boxes.logPanel.getScrollHeight());
  }

  private getLogLevelIcon(level: string): string {
    switch (level) {
      case 'error': return '🔴';
      case 'warn': return '🟡';
      case 'info': return '🔵';
      case 'debug': return '🟣';
      default: return '⚪';
    }
  }

  private getLogLevelColor(level: string): string {
    switch (level) {
      case 'error': return 'red';
      case 'warn': return 'yellow';
      case 'info': return 'blue';
      case 'debug': return 'magenta';
      default: return 'white';
    }
  }

  private showHelp(): void {
    const helpText = `
{bold}{blue-fg}╭─────────────────────────────────────────╮
│           🚀 TAO v2.0 HELP GUIDE        │
╰─────────────────────────────────────────╯{/}

{bold}{cyan-fg}⌨️  KEYBOARD SHORTCUTS{/}
{cyan-fg}├─{/} {bold}F1{/} 📖 Show this help menu
{cyan-fg}├─{/} {bold}F2{/} 📊 Refresh system dashboard
{cyan-fg}├─{/} {bold}F3{/} 🐛 Toggle debug logs
{cyan-fg}├─{/} {bold}F4{/} ⚡ Performance report
{cyan-fg}├─{/} {bold}F5{/} 📈 System statistics
{cyan-fg}╰─{/} {bold}ESC{/} 🚪 Exit application

{bold}{green-fg}💬 CONVERSATION{/}
{green-fg}├─{/} Type any message and press {bold}Enter{/}
{green-fg}├─{/} Master AI will analyze your request
{green-fg}├─{/} Tasks auto-distributed to specialist AIs
{green-fg}╰─{/} Real-time collaboration & optimization

{bold}{yellow-fg}✨ NEW FEATURES{/}
{yellow-fg}├─{/} 🎯 Intelligent task distribution
{yellow-fg}├─{/} 📊 Real-time performance monitoring
{yellow-fg}├─{/} 🧠 Advanced context management
{yellow-fg}├─{/} 🤝 Inter-AI communication
{yellow-fg}╰─{/} 🔄 Adaptive learning system

{bold}{magenta-fg}🖥️  INTERFACE PANELS{/}
{magenta-fg}├─{/} {bold}Left{/}: 📊 System Dashboard & AI Status
{magenta-fg}├─{/} {bold}Center{/}: 💬 AI Conversation Hub
{magenta-fg}├─{/} {bold}Right{/}: 📋 Activity Monitor
{magenta-fg}╰─{/} {bold}Bottom{/}: ✨ Smart Input Interface

{bold}{red-fg}🎮 PRO TIPS{/}
{red-fg}├─{/} Use specific keywords for better AI routing
{red-fg}├─{/} Monitor performance indicators for optimization
{red-fg}├─{/} Check activity logs for system insights
{red-fg}╰─{/} Leverage AI specializations for complex tasks
    `;

    this.addToChatPanel(helpText, 'blue');
    this.screen.render();
  }

  private showPerformanceReport(): void {
    try {
      const report = this.masterAI.getPerformanceReport();

      let reportText = `
{bold}{green-fg}╭─────────────────────────────────────────╮
│           ⚡ PERFORMANCE REPORT          │
╰─────────────────────────────────────────╯{/}

{bold}{cyan-fg}🎯 SYSTEM METRICS{/}
{cyan-fg}├─{/} {bold}Overall Grade:{/} ${this.getGradeIcon(report.systemMetrics.grade)} {bold}${report.systemMetrics.grade}{/}
{cyan-fg}├─{/} {bold}Response Time:{/} ${this.formatResponseTime(report.systemMetrics.averageSystemResponseTime)}
{cyan-fg}├─{/} {bold}Error Rate:{/} ${this.formatErrorRate(report.systemMetrics.errorRate)}
{cyan-fg}├─{/} {bold}Resource Usage:{/} ${this.formatPercentage(report.systemMetrics.resourceUtilization)}
{cyan-fg}╰─{/} {bold}User Satisfaction:{/} ${this.formatRating(report.systemMetrics.userSatisfactionScore)}

{bold}{yellow-fg}🤖 AI WORKER PERFORMANCE{/}`;

      report.workerMetrics.forEach((worker: any, index: number) => {
        const isLast = index === report.workerMetrics.length - 1;
        const prefix = isLast ? '╰─' : '├─';
        const gradeIcon = this.getGradeIcon(worker.grade);

        reportText += `
{yellow-fg}${prefix} {/}${gradeIcon} {bold}${worker.workerName}{/} {gray-fg}(Grade: ${worker.grade}){/}
{yellow-fg}${isLast ? '  ' : '│ '} {/}├─ Success Rate: ${this.formatPercentage(worker.successRate)}
{yellow-fg}${isLast ? '  ' : '│ '} {/}├─ Avg Response: ${this.formatResponseTime(worker.averageResponseTime)}
{yellow-fg}${isLast ? '  ' : '│ '} {/}├─ Efficiency: {bold}${worker.efficiency.toFixed(1)}{/} tasks/min
{yellow-fg}${isLast ? '  ' : '│ '} {/}╰─ Quality Score: ${this.formatRating(worker.qualityScore)}`;
      });

      if (report.recommendations.length > 0) {
        reportText += `

{bold}{magenta-fg}💡 OPTIMIZATION RECOMMENDATIONS{/}`;
        report.recommendations.forEach((rec: string, index: number) => {
          const isLast = index === report.recommendations.length - 1;
          const prefix = isLast ? '╰─' : '├─';
          reportText += `\n{magenta-fg}${prefix} {/}${rec}`;
        });
      }

      this.addToChatPanel(reportText, 'green');
      this.screen.render();
    } catch (error) {
      this.addToChatPanel(`❌ Performance report unavailable: ${error}`, 'red');
    }
  }

  private getGradeIcon(grade: string): string {
    switch (grade) {
      case 'A': return '🏆';
      case 'B': return '🥈';
      case 'C': return '🥉';
      case 'D': return '⚠️';
      case 'F': return '❌';
      default: return '❓';
    }
  }

  private formatPercentage(value: number): string {
    const percentage = (value * 100).toFixed(1);
    if (value >= 0.8) return `{green-fg}${percentage}%{/}`;
    if (value >= 0.6) return `{yellow-fg}${percentage}%{/}`;
    return `{red-fg}${percentage}%{/}`;
  }

  private formatRating(value: number): string {
    const rating = value.toFixed(1);
    if (value >= 4.0) return `{green-fg}${rating}/5 ⭐⭐⭐⭐⭐{/}`;
    if (value >= 3.0) return `{yellow-fg}${rating}/5 ⭐⭐⭐⭐{/}`;
    if (value >= 2.0) return `{yellow-fg}${rating}/5 ⭐⭐⭐{/}`;
    return `{red-fg}${rating}/5 ⭐⭐{/}`;
  }

  private showSystemStatistics(): void {
    try {
      const stats = this.masterAI.getSystemStatistics();

      let statsText = `
{bold}{blue-fg}╭─────────────────────────────────────────╮
│           📈 SYSTEM STATISTICS           │
╰─────────────────────────────────────────╯{/}

{bold}{cyan-fg}🎯 TASK ORCHESTRATOR{/}
{cyan-fg}├─{/} {bold}Active Tasks:{/} ${stats.taskOrchestrator.totalActiveTasks} 🔄
{cyan-fg}├─{/} {bold}Task History:{/} ${stats.taskOrchestrator.taskHistory} 📋
{cyan-fg}╰─{/} {bold}Success Rate:{/} ${this.calculateSuccessRate(stats)}

{bold}{green-fg}🧠 CONTEXT MANAGEMENT{/}
{green-fg}├─{/} {bold}Session ID:{/} {gray-fg}${stats.context.sessionId.substring(0, 12)}...{/}
{green-fg}├─{/} {bold}Messages:{/} ${stats.context.totalMessages} 💬
{green-fg}├─{/} {bold}Active Topics:{/} ${this.formatTopics(stats.context.currentTopics)}
{green-fg}├─{/} {bold}Summaries:{/} ${stats.context.summaryCount} 📝
{green-fg}╰─{/} {bold}Avg Importance:{/} ${this.formatImportance(stats.context.averageImportance)}

{bold}{yellow-fg}🔄 COMMUNICATION PROTOCOL{/}
{yellow-fg}├─{/} {bold}Active Queues:{/} ${stats.communication.activeQueues} 📤
{yellow-fg}├─{/} {bold}Pending Messages:{/} ${stats.communication.totalQueuedMessages} ⏳
{yellow-fg}├─{/} {bold}Collaborations:{/} ${stats.communication.activeCollaborations} 🤝
{yellow-fg}├─{/} {bold}Knowledge Base:{/} ${stats.communication.knowledgeBaseSize} 🧠
{yellow-fg}╰─{/} {bold}Handlers:{/} ${stats.communication.registeredHandlers} 🔧

{bold}{magenta-fg}⚡ SYSTEM PERFORMANCE{/}
{magenta-fg}├─{/} {bold}Uptime:{/} ${this.formatUptime(stats.performance.systemUptime)}
{magenta-fg}├─{/} {bold}Total Requests:{/} ${stats.performance.totalRequests} 📊
{magenta-fg}├─{/} {bold}Throughput:{/} ${stats.performance.taskThroughput.toFixed(1)} tasks/hour 🚀
{magenta-fg}├─{/} {bold}Active Workers:{/} ${stats.performance.activeWorkers} 🤖
{magenta-fg}╰─{/} {bold}Resource Usage:{/} ${this.formatPercentage(stats.performance.resourceUtilization)}

{bold}{red-fg}🎮 REAL-TIME METRICS{/}
{red-fg}├─{/} {bold}Memory Usage:{/} ${this.getMemoryUsage()}
{red-fg}├─{/} {bold}CPU Load:{/} ${this.getCPULoad()}
{red-fg}╰─{/} {bold}Network Status:{/} ${this.getNetworkStatus()}`;

      this.addToChatPanel(statsText, 'blue');
      this.screen.render();
    } catch (error) {
      this.addToChatPanel(`❌ System statistics unavailable: ${error}`, 'red');
    }
  }

  private calculateSuccessRate(stats: any): string {
    const errorRate = stats.performance.errorRate;
    const successRate = 1 - errorRate;
    return this.formatPercentage(successRate);
  }

  private formatTopics(topics: string[]): string {
    if (!topics || topics.length === 0) return '{gray-fg}None{/}';
    const topicList = topics.slice(0, 3).join(', ');
    const extra = topics.length > 3 ? ` +${topics.length - 3}` : '';
    return `{white-fg}${topicList}${extra}{/}`;
  }

  private formatImportance(importance: number): string {
    if (importance >= 0.8) return `{green-fg}${(importance * 100).toFixed(0)}% High{/}`;
    if (importance >= 0.5) return `{yellow-fg}${(importance * 100).toFixed(0)}% Medium{/}`;
    return `{red-fg}${(importance * 100).toFixed(0)}% Low{/}`;
  }

  private formatUptime(uptime: number): string {
    const hours = Math.floor(uptime / (1000 * 60 * 60));
    const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    return `{green-fg}${hours}h ${minutes}m{/} ⏰`;
  }

  private getMemoryUsage(): string {
    try {
      const used = process.memoryUsage();
      const mb = Math.round(used.heapUsed / 1024 / 1024);
      if (mb < 100) return `{green-fg}${mb}MB{/}`;
      if (mb < 200) return `{yellow-fg}${mb}MB{/}`;
      return `{red-fg}${mb}MB{/}`;
    } catch {
      return '{gray-fg}N/A{/}';
    }
  }

  private getCPULoad(): string {
    // Simplified CPU load indicator
    const load = Math.random() * 100; // In real app, use actual CPU metrics
    if (load < 50) return `{green-fg}${load.toFixed(0)}%{/}`;
    if (load < 80) return `{yellow-fg}${load.toFixed(0)}%{/}`;
    return `{red-fg}${load.toFixed(0)}%{/}`;
  }

  private getNetworkStatus(): string {
    return '{green-fg}Online{/} 🌐';
  }

  async start(): Promise<void> {
    // İlk durumu güncelle
    this.updateWorkerStatus();
    this.updateLogPanel();
    
    // Input alanına odaklan
    this.boxes.inputPanel.focus();
    
    // Ekranı render et
    this.screen.render();
    
    // Log güncellemelerini dinle
    setInterval(() => {
      this.updateLogPanel();
      this.screen.render();
    }, 2000);
  }
}