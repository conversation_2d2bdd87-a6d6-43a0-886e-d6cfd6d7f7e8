#!/usr/bin/env node

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import { MasterAI } from './master-ai';
import { Logger } from './logger';
import { SystemConfig } from './types';
import { TerminalUI } from './terminal-ui';

class TerminalAIOrchestrator {
  private config!: SystemConfig;
  private masterAI!: MasterAI;
  public logger: Logger;
  private ui!: TerminalUI;
  private isRunning: boolean = false;

  constructor() {
    this.logger = new Logger(false);
    this.loadConfiguration();
    this.initializeSystem();
  }

  private loadConfiguration(): void {
    const configPath = join(process.cwd(), 'config', 'models.json');
    
    if (!existsSync(configPath)) {
      this.logger.error('System', `Konfigürasyon dosyası bulunamadı: ${configPath}`);
      process.exit(1);
    }

    try {
      const configData = readFileSync(configPath, 'utf-8');
      this.config = JSON.parse(configData);
      this.logger.info('System', 'Konfigürasyon yüklendi');
    } catch (error) {
      this.logger.error('System', `Konfigürasyon yükleme hatası: ${error}`);
      process.exit(1);
    }
  }

  private initializeSystem(): void {
    try {
      // Worker ID'lerini ata
      this.config.workers.forEach((worker, index) => {
        if (!worker.id) {
          worker.id = `worker_${index + 1}`;
        }
      });

      this.masterAI = new MasterAI(
        this.config.master,
        this.config.workers,
        this.logger
      );

      this.ui = new TerminalUI(this.masterAI, this.logger);
      
      this.logger.info('System', 'Terminal AI Orchestrator başlatıldı');
    } catch (error) {
      this.logger.error('System', `Sistem başlatma hatası: ${error}`);
      process.exit(1);
    }
  }

  async start(): Promise<void> {
    this.isRunning = true;
    
    console.log(chalk.cyan.bold('\n🤖 Terminal AI Orchestrator'));
    console.log(chalk.gray('Çoklu AI modeli orkestrasyon sistemi\n'));
    
    this.showSystemInfo();
    
    await this.ui.start();
  }

  private showSystemInfo(): void {
    console.log(chalk.yellow('📋 Sistem Bilgileri:'));
    console.log(`   Master AI: ${chalk.green(this.config.master.name)}`);
    console.log(`   Worker AI'lar: ${this.config.workers.length} adet`);
    
    this.config.workers.forEach(worker => {
      console.log(`   - ${chalk.blue(worker.name)} (${worker.specialization})`);
    });
    
    console.log(`   İletişim: ${this.config.communication.enableInterWorkerCommunication ? chalk.green('Aktif') : chalk.red('Pasif')}`);
    console.log('');
  }

  async interactiveMode(): Promise<void> {
    while (this.isRunning) {
      try {
        const { input } = await inquirer.prompt([
          {
            type: 'input',
            name: 'input',
            message: chalk.cyan('Siz:'),
            prefix: '💬'
          }
        ]);

        if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'çıkış') {
          this.isRunning = false;
          console.log(chalk.yellow('Görüşmek üzere! 👋'));
          break;
        }

        if (input.toLowerCase() === 'help' || input.toLowerCase() === 'yardım') {
          this.showHelp();
          continue;
        }

        if (input.toLowerCase() === 'status' || input.toLowerCase() === 'durum') {
          this.showStatus();
          continue;
        }

        console.log(chalk.gray('🤔 İşleniyor...'));
        const response = await this.masterAI.processUserInput(input);
        console.log(chalk.green(`🤖 Master AI: ${response}\n`));

      } catch (error) {
        console.log(chalk.red(`❌ Hata: ${error}\n`));
      }
    }
  }

  private showHelp(): void {
    console.log(chalk.yellow('\n📖 Yardım:'));
    console.log('  help/yardım  - Bu yardım mesajını göster');
    console.log('  status/durum - Sistem durumunu göster');
    console.log('  exit/çıkış   - Uygulamadan çık');
    console.log('  Diğer tüm girişler Master AI\'a gönderilir\n');
  }

  private showStatus(): void {
    console.log(chalk.yellow('\n📊 Sistem Durumu:'));
    
    const workers = this.masterAI.getWorkers();
    workers.forEach(worker => {
      const status = worker.isAvailable() ? chalk.green('Müsait') : chalk.yellow('Meşgul');
      console.log(`  ${worker.getName()}: ${status} - ${worker.getStatus()}`);
    });
    
    const activeTasks = this.masterAI.getActiveTasks();
    console.log(`  Aktif Görevler: ${activeTasks.length}`);
    console.log('');
  }
}

// CLI setup
const program = new Command();

program
  .name('tao')
  .description('Terminal AI Orchestrator - Çoklu AI modeli orkestrasyon sistemi')
  .version('1.0.0');

program
  .command('start')
  .description('Sistemi başlat')
  .option('-d, --debug', 'Debug modunu etkinleştir')
  .option('-i, --interactive', 'İnteraktif mod (varsayılan)')
  .action(async (options) => {
    const tao = new TerminalAIOrchestrator();
    
    if (options.debug) {
      tao.logger.setDebugMode(true);
    }
    
    await tao.start();
    
    if (options.interactive !== false) {
      await tao.interactiveMode();
    }
  });

program
  .command('config')
  .description('Konfigürasyonu göster')
  .action(() => {
    const configPath = join(process.cwd(), 'config', 'models.json');
    if (existsSync(configPath)) {
      const config = JSON.parse(readFileSync(configPath, 'utf-8'));
      console.log(JSON.stringify(config, null, 2));
    } else {
      console.log(chalk.red('Konfigürasyon dosyası bulunamadı'));
    }
  });

// Ana program
if (require.main === module) {
  program.parse();
}

export { TerminalAIOrchestrator };