import axios, { AxiosInstance } from 'axios';
import { AIModel, AIResponse, Message } from './types';

export class AIService {
  private client: AxiosInstance;
  private model: AIModel;

  constructor(model: AIModel) {
    this.model = model;
    this.client = axios.create({
      baseURL: model.provider.baseURL,
      headers: {
        'Authorization': `Bearer ${model.provider.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
  }

  async sendMessage(messages: Message[], context?: string): Promise<AIResponse> {
    try {
      const formattedMessages = this.formatMessages(messages, context);
      
      const response = await this.client.post('/chat/completions', {
        model: this.model.provider.model,
        messages: formattedMessages,
        max_tokens: this.model.maxTokens,
        temperature: this.model.temperature,
        stream: false
      });

      return {
        content: response.data.choices[0].message.content,
        usage: response.data.usage
      };
    } catch (error) {
      throw new Error(`AI Service Error: ${error}`);
    }
  }

  private formatMessages(messages: Message[], context?: string): any[] {
    const formatted = [
      {
        role: 'system',
        content: this.buildSystemPrompt(context)
      }
    ];

    messages.forEach(msg => {
      if (msg.type === 'user') {
        formatted.push({
          role: 'user',
          content: msg.content
        });
      } else if (msg.type === 'ai_response') {
        formatted.push({
          role: 'assistant',
          content: msg.content
        });
      }
    });

    return formatted;
  }

  private buildSystemPrompt(context?: string): string {
    let prompt = this.model.systemPrompt;
    
    if (context) {
      prompt += `\n\nEk Bağlam: ${context}`;
    }

    if (this.model.role === 'master') {
      prompt += `\n\nMevcut Yeteneklerin: ${this.model.capabilities.join(', ')}`;
      prompt += `\n\nWorker AI'lar ile iletişim kurabilir, görevleri dağıtabilir ve sonuçları koordine edebilirsin.`;
    } else {
      prompt += `\n\nUzmanlık Alanın: ${this.model.specialization}`;
      prompt += `\n\nYeteneklerin: ${this.model.capabilities.join(', ')}`;
      prompt += `\n\nMaster AI'dan gelen görevleri yerine getirirsin ve diğer worker AI'lar ile işbirliği yapabilirsin.`;
    }

    return prompt;
  }

  getModelInfo(): AIModel {
    return this.model;
  }

  updateModel(updates: Partial<AIModel>): void {
    this.model = { ...this.model, ...updates };
  }
}