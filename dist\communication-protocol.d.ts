import { Task } from './types';
import { Logger } from './logger';
export interface CommunicationMessage {
    id: string;
    type: 'task_assignment' | 'task_result' | 'collaboration_request' | 'status_update' | 'knowledge_share' | 'coordination';
    from: string;
    to: string | string[];
    content: any;
    priority: 'low' | 'normal' | 'high' | 'urgent';
    timestamp: Date;
    requiresResponse: boolean;
    correlationId?: string;
    metadata?: any;
}
export interface CollaborationRequest {
    requestId: string;
    initiator: string;
    collaborators: string[];
    objective: string;
    taskContext: any;
    coordinationStrategy: 'parallel' | 'sequential' | 'hybrid';
    deadline?: Date;
}
export interface KnowledgeShare {
    shareId: string;
    source: string;
    knowledge: any;
    relevantWorkers: string[];
    knowledgeType: 'insight' | 'solution' | 'pattern' | 'error' | 'optimization';
    confidence: number;
}
export declare class CommunicationProtocol {
    private messageQueue;
    private activeCollaborations;
    private knowledgeBase;
    private messageHandlers;
    private logger;
    private readonly MESSAGE_TIMEOUT;
    private readonly MAX_QUEUE_SIZE;
    private readonly KNOWLEDGE_RETENTION_HOURS;
    constructor(logger: Logger);
    private initializeProtocol;
    registerMessageHandler(workerId: string, handler: Function): void;
    sendMessage(message: Omit<CommunicationMessage, 'id' | 'timestamp'>): string;
    private processMessageQueues;
    private deliverMessage;
    private retryMessage;
    private getPriorityWeight;
    initiateCollaboration(request: Omit<CollaborationRequest, 'requestId'>): string;
    respondToCollaboration(requestId: string, workerId: string, accepted: boolean, capabilities?: string[]): void;
    shareKnowledge(share: Omit<KnowledgeShare, 'shareId'>): string;
    queryKnowledge(workerId: string, query: string, knowledgeTypes?: string[]): KnowledgeShare[];
    coordinateTaskExecution(tasks: Task[], strategy: 'parallel' | 'sequential' | 'hybrid'): string;
    private createCoordinationPlan;
    private groupRelatedTasks;
    private areTasksRelated;
    private cleanupKnowledgeBase;
    getProtocolStatistics(): any;
    exportCommunicationData(): any;
}
//# sourceMappingURL=communication-protocol.d.ts.map