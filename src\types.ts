export interface AIProvider {
  baseURL: string;
  apiKey: string;
  model: string;
}

export interface AIModel {
  id?: string;
  name: string;
  role: 'master' | 'worker';
  specialization?: string;
  provider: AIProvider;
  systemPrompt: string;
  capabilities: string[];
  maxTokens: number;
  temperature: number;
}

export interface Message {
  id: string;
  from: string;
  to: string;
  content: string;
  timestamp: Date;
  type: 'user' | 'system' | 'ai_response' | 'inter_ai';
  metadata?: any;
}

export interface Task {
  id: string;
  description: string;
  assignedTo: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  result?: string;
  createdAt: Date;
  completedAt?: Date;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  complexity?: number;
  estimatedTime?: number;
  retryCount?: number;
  metadata?: any;
}

export interface SystemConfig {
  master: AIModel;
  workers: AIModel[];
  communication: {
    enableInterWorkerCommunication: boolean;
    messageTimeout: number;
    maxRetries: number;
  };
}

export interface AIResponse {
  content: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}export interface ConversationContext {
  messages: Message[];
  activeTask?: Task;
  participants: string[];
  sessionId?: string;
  activeTopics?: string[];
  contextSummaries?: any[];
  userPreferences?: Map<string, any>;
}

export interface UIState {
  currentView: 'main' | 'config' | 'logs' | 'tasks';
  selectedModel?: string;
  showDebug: boolean;
}

export interface LogEntry {
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  source: string;
  message: string;
  data?: any;
}