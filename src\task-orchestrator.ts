import { Task, AIModel, Message } from './types';
import { WorkerAI } from './worker-ai';
import { Logger } from './logger';
import { v4 as uuidv4 } from 'uuid';

export interface TaskPriority {
  level: 'low' | 'normal' | 'high' | 'urgent';
  weight: number;
}

export interface WorkerCapability {
  name: string;
  proficiency: number; // 0-1 scale
  currentLoad: number; // 0-1 scale
  averageResponseTime: number; // milliseconds
  successRate: number; // 0-1 scale
}

export interface TaskAnalysis {
  complexity: number; // 0-1 scale
  estimatedTime: number; // milliseconds
  requiredCapabilities: string[];
  priority: TaskPriority;
  canBeSplit: boolean;
  dependencies: string[];
}

export class TaskOrchestrator {
  private workers: Map<string, WorkerAI> = new Map();
  private taskQueue: Task[] = [];
  private activeTasksPerWorker: Map<string, number> = new Map();
  private workerCapabilities: Map<string, WorkerCapability[]> = new Map();
  private taskHistory: Task[] = [];
  private logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  registerWorker(worker: WorkerAI): void {
    this.workers.set(worker.getId(), worker);
    this.activeTasksPerWorker.set(worker.getId(), 0);
    this.initializeWorkerCapabilities(worker);
  }

  private initializeWorkerCapabilities(worker: WorkerAI): void {
    const model = worker.getModel();
    const capabilities: WorkerCapability[] = model.capabilities.map((cap: string) => ({
      name: cap,
      proficiency: this.calculateInitialProficiency(cap, model.specialization),
      currentLoad: 0,
      averageResponseTime: 2000, // Default 2 seconds
      successRate: 1.0 // Start with perfect score
    }));
    
    this.workerCapabilities.set(worker.getId(), capabilities);
  }

  private calculateInitialProficiency(capability: string, specialization?: string): number {
    if (specialization && capability.includes(specialization.replace('_', ''))) {
      return 0.9; // High proficiency for specialized capabilities
    }
    return 0.7; // Default proficiency
  }

  async analyzeTask(description: string, context?: string): Promise<TaskAnalysis> {
    // Advanced task analysis using NLP patterns and keywords
    const complexity = this.calculateComplexity(description);
    const estimatedTime = this.estimateTaskTime(description, complexity);
    const requiredCapabilities = this.identifyRequiredCapabilities(description);
    const priority = this.determinePriority(description, context);
    const canBeSplit = this.canTaskBeSplit(description);
    
    return {
      complexity,
      estimatedTime,
      requiredCapabilities,
      priority,
      canBeSplit,
      dependencies: []
    };
  }

  private calculateComplexity(description: string): number {
    let complexity = 0.3; // Base complexity
    
    // Complexity indicators
    const complexityIndicators = [
      { pattern: /analiz|analysis|debug|optimize/i, weight: 0.2 },
      { pattern: /kod|code|function|class|algorithm/i, weight: 0.15 },
      { pattern: /araştır|research|investigate/i, weight: 0.1 },
      { pattern: /karmaşık|complex|advanced|sophisticated/i, weight: 0.25 },
      { pattern: /multiple|birden|çoklu/i, weight: 0.15 },
      { pattern: /integration|entegrasyon|combine/i, weight: 0.2 }
    ];

    complexityIndicators.forEach(indicator => {
      if (indicator.pattern.test(description)) {
        complexity += indicator.weight;
      }
    });

    return Math.min(complexity, 1.0);
  }

  private estimateTaskTime(description: string, complexity: number): number {
    const baseTime = 5000; // 5 seconds base
    const complexityMultiplier = 1 + (complexity * 3); // 1x to 4x multiplier
    const lengthFactor = Math.min(description.length / 100, 2); // Length factor
    
    return Math.round(baseTime * complexityMultiplier * lengthFactor);
  }

  private identifyRequiredCapabilities(description: string): string[] {
    const capabilities: string[] = [];
    
    const capabilityPatterns = [
      { pattern: /kod|code|debug|function|class/i, capability: 'code_analysis' },
      { pattern: /araştır|research|bilgi|information/i, capability: 'research' },
      { pattern: /yaz|write|create|oluştur/i, capability: 'creative_writing' },
      { pattern: /analiz|analysis|examine/i, capability: 'analysis' },
      { pattern: /optimize|iyileştir|improve/i, capability: 'optimization' },
      { pattern: /test|doğrula|verify/i, capability: 'fact_checking' },
      { pattern: /tasarım|design|concept/i, capability: 'visual_concepts' },
      { pattern: /strateji|strategy|plan/i, capability: 'content_strategy' }
    ];

    capabilityPatterns.forEach(pattern => {
      if (pattern.pattern.test(description)) {
        capabilities.push(pattern.capability);
      }
    });

    return capabilities.length > 0 ? capabilities : ['general'];
  }

  private determinePriority(description: string, context?: string): TaskPriority {
    let weight = 50; // Default normal priority
    
    // Priority indicators
    if (/urgent|acil|hemen|immediately/i.test(description)) weight += 40;
    if (/important|önemli|critical|kritik/i.test(description)) weight += 30;
    if (/quick|hızlı|fast|çabuk/i.test(description)) weight += 20;
    if (/later|sonra|eventually/i.test(description)) weight -= 20;
    
    if (weight >= 80) return { level: 'urgent', weight };
    if (weight >= 65) return { level: 'high', weight };
    if (weight >= 35) return { level: 'normal', weight };
    return { level: 'low', weight };
  }

  private canTaskBeSplit(description: string): boolean {
    const splitIndicators = [
      /and|ve|also|ayrıca/i,
      /multiple|birden|çoklu/i,
      /list|liste|enumerate/i,
      /both|her ikisi|ikisi de/i
    ];
    
    return splitIndicators.some(pattern => pattern.test(description));
  }

  async findOptimalWorker(taskAnalysis: TaskAnalysis): Promise<WorkerAI | null> {
    let bestWorker: WorkerAI | null = null;
    let bestScore = -1;

    for (const [workerId, worker] of this.workers) {
      if (!worker.isAvailable()) continue;

      const score = this.calculateWorkerScore(workerId, taskAnalysis);
      
      if (score > bestScore) {
        bestScore = score;
        bestWorker = worker;
      }
    }

    this.logger.debug('TaskOrchestrator', 
      `Best worker for task: ${bestWorker?.getName()} (score: ${bestScore.toFixed(2)})`);

    return bestWorker;
  }

  private calculateWorkerScore(workerId: string, taskAnalysis: TaskAnalysis): number {
    const capabilities = this.workerCapabilities.get(workerId) || [];
    const currentLoad = this.activeTasksPerWorker.get(workerId) || 0;
    
    let capabilityScore = 0;
    let totalProficiency = 0;
    let matchedCapabilities = 0;

    // Calculate capability match score
    taskAnalysis.requiredCapabilities.forEach(requiredCap => {
      const capability = capabilities.find(cap => 
        cap.name === requiredCap || cap.name.includes(requiredCap)
      );
      
      if (capability) {
        capabilityScore += capability.proficiency * capability.successRate;
        totalProficiency += capability.proficiency;
        matchedCapabilities++;
      }
    });

    // Normalize capability score
    if (matchedCapabilities > 0) {
      capabilityScore = capabilityScore / matchedCapabilities;
    } else {
      capabilityScore = 0.3; // Default score for unmatched capabilities
    }

    // Load balancing factor (prefer less loaded workers)
    const loadFactor = Math.max(0.1, 1 - (currentLoad * 0.3));
    
    // Priority factor
    const priorityFactor = taskAnalysis.priority.weight / 100;
    
    // Final score calculation
    const finalScore = (capabilityScore * 0.6) + (loadFactor * 0.3) + (priorityFactor * 0.1);
    
    this.logger.debug('TaskOrchestrator', 
      `Worker ${workerId} score: ${finalScore.toFixed(2)} (cap: ${capabilityScore.toFixed(2)}, load: ${loadFactor.toFixed(2)}, priority: ${priorityFactor.toFixed(2)})`);

    return finalScore;
  }

  async distributeTask(description: string, context?: string): Promise<Task | null> {
    try {
      const taskAnalysis = await this.analyzeTask(description, context);
      const optimalWorker = await this.findOptimalWorker(taskAnalysis);

      if (!optimalWorker) {
        this.logger.warn('TaskOrchestrator', 'No available worker found for task');
        return null;
      }

      const task: Task = {
        id: uuidv4(),
        description,
        assignedTo: optimalWorker.getId(),
        status: 'pending',
        createdAt: new Date(),
        metadata: {
          analysis: taskAnalysis,
          assignedWorkerName: optimalWorker.getName()
        }
      };

      // Update worker load
      const currentLoad = this.activeTasksPerWorker.get(optimalWorker.getId()) || 0;
      this.activeTasksPerWorker.set(optimalWorker.getId(), currentLoad + 1);

      this.logger.info('TaskOrchestrator', 
        `Task assigned to ${optimalWorker.getName()}: ${description.substring(0, 50)}...`);

      return task;
    } catch (error) {
      this.logger.error('TaskOrchestrator', `Task distribution error: ${error}`);
      return null;
    }
  }

  updateWorkerPerformance(workerId: string, task: Task, responseTime: number, success: boolean): void {
    const capabilities = this.workerCapabilities.get(workerId);
    if (!capabilities) return;

    const taskAnalysis = task.metadata?.analysis as TaskAnalysis;
    if (!taskAnalysis) return;

    // Update relevant capabilities
    taskAnalysis.requiredCapabilities.forEach(requiredCap => {
      const capability = capabilities.find(cap => 
        cap.name === requiredCap || cap.name.includes(requiredCap)
      );
      
      if (capability) {
        // Update success rate (exponential moving average)
        capability.successRate = (capability.successRate * 0.8) + (success ? 0.2 : 0);
        
        // Update average response time
        capability.averageResponseTime = (capability.averageResponseTime * 0.7) + (responseTime * 0.3);
        
        // Adjust proficiency based on performance
        if (success && responseTime < capability.averageResponseTime) {
          capability.proficiency = Math.min(1.0, capability.proficiency + 0.01);
        } else if (!success) {
          capability.proficiency = Math.max(0.1, capability.proficiency - 0.02);
        }
      }
    });

    // Update active task count
    const currentLoad = this.activeTasksPerWorker.get(workerId) || 0;
    this.activeTasksPerWorker.set(workerId, Math.max(0, currentLoad - 1));

    this.logger.debug('TaskOrchestrator', 
      `Updated performance for worker ${workerId}: success=${success}, time=${responseTime}ms`);
  }

  getWorkerStatistics(workerId: string): any {
    const capabilities = this.workerCapabilities.get(workerId);
    const currentLoad = this.activeTasksPerWorker.get(workerId);
    
    return {
      workerId,
      currentLoad,
      capabilities: capabilities?.map(cap => ({
        name: cap.name,
        proficiency: Math.round(cap.proficiency * 100),
        successRate: Math.round(cap.successRate * 100),
        avgResponseTime: Math.round(cap.averageResponseTime)
      }))
    };
  }

  getAllStatistics(): any {
    const stats: any = {};
    
    for (const workerId of this.workers.keys()) {
      stats[workerId] = this.getWorkerStatistics(workerId);
    }
    
    return {
      workers: stats,
      totalActiveTasks: Array.from(this.activeTasksPerWorker.values()).reduce((a, b) => a + b, 0),
      taskHistory: this.taskHistory.length
    };
  }
}
