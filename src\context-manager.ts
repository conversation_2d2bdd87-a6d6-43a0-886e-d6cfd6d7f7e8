import { Message, ConversationContext, Task } from './types';
import { Logger } from './logger';

export interface ContextSummary {
  id: string;
  summary: string;
  keyTopics: string[];
  participants: string[];
  timeRange: {
    start: Date;
    end: Date;
  };
  messageCount: number;
  importance: number; // 0-1 scale
}

export interface ContextWindow {
  recentMessages: Message[];
  relevantSummaries: ContextSummary[];
  activeTopics: string[];
  userPreferences: Map<string, any>;
  sessionMetadata: any;
}

export class ContextManager {
  private conversationHistory: Message[] = [];
  private contextSummaries: ContextSummary[] = [];
  private activeTopics: Set<string> = new Set();
  private userPreferences: Map<string, any> = new Map();
  private sessionMetadata: any = {};
  private logger: Logger;
  
  // Configuration
  private readonly MAX_RECENT_MESSAGES = 20;
  private readonly MAX_CONTEXT_SUMMARIES = 10;
  private readonly SUMMARY_THRESHOLD = 50; // Messages before creating summary
  private readonly RELEVANCE_THRESHOLD = 0.3;

  constructor(logger: Logger) {
    this.logger = logger;
    this.initializeSession();
  }

  private initializeSession(): void {
    this.sessionMetadata = {
      sessionId: this.generateSessionId(),
      startTime: new Date(),
      messageCount: 0,
      topicSwitches: 0,
      averageResponseTime: 0
    };
    
    this.logger.info('ContextManager', `New session initialized: ${this.sessionMetadata.sessionId}`);
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  addMessage(message: Message): void {
    this.conversationHistory.push(message);
    this.sessionMetadata.messageCount++;
    
    // Extract topics from message content
    this.extractAndUpdateTopics(message.content);
    
    // Check if we need to create a summary
    if (this.conversationHistory.length >= this.SUMMARY_THRESHOLD) {
      this.createContextSummary();
    }
    
    this.logger.debug('ContextManager', `Message added: ${message.type} from ${message.from}`);
  }

  private extractAndUpdateTopics(content: string): void {
    // Simple topic extraction using keywords and patterns
    const topicPatterns = [
      { pattern: /kod|code|programming|yazılım/i, topic: 'programming' },
      { pattern: /araştırma|research|bilgi|information/i, topic: 'research' },
      { pattern: /tasarım|design|creative|yaratıcı/i, topic: 'design' },
      { pattern: /analiz|analysis|data|veri/i, topic: 'analysis' },
      { pattern: /problem|sorun|hata|error|bug/i, topic: 'troubleshooting' },
      { pattern: /optimize|iyileştir|performance|performans/i, topic: 'optimization' },
      { pattern: /test|doğrula|verify|kontrol/i, topic: 'testing' },
      { pattern: /dokümantasyon|documentation|guide|kılavuz/i, topic: 'documentation' }
    ];

    const previousTopics = new Set(this.activeTopics);
    
    topicPatterns.forEach(({ pattern, topic }) => {
      if (pattern.test(content)) {
        this.activeTopics.add(topic);
      }
    });

    // Detect topic switches
    const newTopics = new Set([...this.activeTopics].filter(topic => !previousTopics.has(topic)));
    if (newTopics.size > 0) {
      this.sessionMetadata.topicSwitches++;
      this.logger.debug('ContextManager', `New topics detected: ${Array.from(newTopics).join(', ')}`);
    }

    // Remove old topics if conversation has moved on
    this.pruneInactiveTopics();
  }

  private pruneInactiveTopics(): void {
    const recentMessages = this.conversationHistory.slice(-10);
    const recentContent = recentMessages.map(m => m.content).join(' ');
    
    const topicsToRemove: string[] = [];
    
    this.activeTopics.forEach(topic => {
      const topicPattern = new RegExp(topic, 'i');
      if (!topicPattern.test(recentContent)) {
        topicsToRemove.push(topic);
      }
    });

    topicsToRemove.forEach(topic => {
      this.activeTopics.delete(topic);
    });
  }

  private createContextSummary(): void {
    const messagesToSummarize = this.conversationHistory.slice(-this.SUMMARY_THRESHOLD);
    
    if (messagesToSummarize.length === 0) return;

    const summary: ContextSummary = {
      id: `summary_${Date.now()}`,
      summary: this.generateSummary(messagesToSummarize),
      keyTopics: Array.from(this.activeTopics),
      participants: [...new Set(messagesToSummarize.map(m => m.from))],
      timeRange: {
        start: messagesToSummarize[0].timestamp,
        end: messagesToSummarize[messagesToSummarize.length - 1].timestamp
      },
      messageCount: messagesToSummarize.length,
      importance: this.calculateImportance(messagesToSummarize)
    };

    this.contextSummaries.push(summary);
    
    // Keep only the most recent summaries
    if (this.contextSummaries.length > this.MAX_CONTEXT_SUMMARIES) {
      this.contextSummaries = this.contextSummaries
        .sort((a, b) => b.importance - a.importance)
        .slice(0, this.MAX_CONTEXT_SUMMARIES);
    }

    // Remove summarized messages from active history
    this.conversationHistory = this.conversationHistory.slice(-10);
    
    this.logger.info('ContextManager', `Context summary created: ${summary.keyTopics.join(', ')}`);
  }

  private generateSummary(messages: Message[]): string {
    // Simple extractive summarization
    const userMessages = messages.filter(m => m.type === 'user');
    const aiResponses = messages.filter(m => m.type === 'ai_response');
    
    const mainTopics = Array.from(this.activeTopics).slice(0, 3);
    const keyUserQuestions = userMessages
      .map(m => m.content)
      .filter(content => content.includes('?') || content.length > 20)
      .slice(0, 2);

    let summary = `Conversation covered ${mainTopics.join(', ')}. `;
    
    if (keyUserQuestions.length > 0) {
      summary += `Key questions: ${keyUserQuestions.map(q => q.substring(0, 50)).join('; ')}. `;
    }
    
    summary += `${userMessages.length} user messages, ${aiResponses.length} AI responses.`;
    
    return summary;
  }

  private calculateImportance(messages: Message[]): number {
    let importance = 0.5; // Base importance
    
    // Factors that increase importance
    const userMessages = messages.filter(m => m.type === 'user').length;
    const questionCount = messages.filter(m => m.content.includes('?')).length;
    const longMessages = messages.filter(m => m.content.length > 100).length;
    const topicCount = this.activeTopics.size;
    
    importance += (userMessages / messages.length) * 0.2; // User engagement
    importance += (questionCount / messages.length) * 0.15; // Question density
    importance += (longMessages / messages.length) * 0.1; // Message depth
    importance += Math.min(topicCount * 0.05, 0.15); // Topic diversity
    
    return Math.min(importance, 1.0);
  }

  getContextWindow(currentMessage?: string): ContextWindow {
    const recentMessages = this.conversationHistory.slice(-this.MAX_RECENT_MESSAGES);
    const relevantSummaries = this.findRelevantSummaries(currentMessage);
    
    return {
      recentMessages,
      relevantSummaries,
      activeTopics: Array.from(this.activeTopics),
      userPreferences: this.userPreferences,
      sessionMetadata: this.sessionMetadata
    };
  }

  private findRelevantSummaries(currentMessage?: string): ContextSummary[] {
    if (!currentMessage) {
      return this.contextSummaries
        .sort((a, b) => b.importance - a.importance)
        .slice(0, 3);
    }

    // Find summaries with relevant topics
    const relevantSummaries = this.contextSummaries.filter(summary => {
      const relevanceScore = this.calculateRelevanceScore(summary, currentMessage);
      return relevanceScore >= this.RELEVANCE_THRESHOLD;
    });

    return relevantSummaries
      .sort((a, b) => b.importance - a.importance)
      .slice(0, 3);
  }

  private calculateRelevanceScore(summary: ContextSummary, message: string): number {
    let score = 0;
    
    // Topic overlap
    const messageTopics = this.extractTopicsFromText(message);
    const topicOverlap = summary.keyTopics.filter(topic => 
      messageTopics.includes(topic)
    ).length;
    
    if (summary.keyTopics.length > 0) {
      score += (topicOverlap / summary.keyTopics.length) * 0.6;
    }
    
    // Keyword similarity (simple approach)
    const summaryWords = summary.summary.toLowerCase().split(' ');
    const messageWords = message.toLowerCase().split(' ');
    const commonWords = summaryWords.filter(word => 
      messageWords.includes(word) && word.length > 3
    ).length;
    
    score += Math.min(commonWords / Math.max(messageWords.length, 1), 0.4);
    
    // Recency factor
    const timeDiff = Date.now() - summary.timeRange.end.getTime();
    const recencyFactor = Math.max(0, 1 - (timeDiff / (24 * 60 * 60 * 1000))); // 24 hours
    score += recencyFactor * 0.2;
    
    return score;
  }

  private extractTopicsFromText(text: string): string[] {
    const topics: string[] = [];
    
    const topicPatterns = [
      { pattern: /kod|code|programming/i, topic: 'programming' },
      { pattern: /araştırma|research/i, topic: 'research' },
      { pattern: /tasarım|design/i, topic: 'design' },
      { pattern: /analiz|analysis/i, topic: 'analysis' },
      { pattern: /problem|sorun/i, topic: 'troubleshooting' },
      { pattern: /optimize|iyileştir/i, topic: 'optimization' }
    ];

    topicPatterns.forEach(({ pattern, topic }) => {
      if (pattern.test(text)) {
        topics.push(topic);
      }
    });

    return topics;
  }

  updateUserPreference(key: string, value: any): void {
    this.userPreferences.set(key, value);
    this.logger.debug('ContextManager', `User preference updated: ${key}`);
  }

  getUserPreference(key: string): any {
    return this.userPreferences.get(key);
  }

  getSessionStatistics(): any {
    return {
      ...this.sessionMetadata,
      currentTopics: Array.from(this.activeTopics),
      summaryCount: this.contextSummaries.length,
      totalMessages: this.conversationHistory.length,
      averageImportance: this.contextSummaries.length > 0 
        ? this.contextSummaries.reduce((sum, s) => sum + s.importance, 0) / this.contextSummaries.length 
        : 0
    };
  }

  buildContextString(includeHistory: boolean = true): string {
    const contextWindow = this.getContextWindow();
    let contextString = '';

    // Add session info
    contextString += `Session: ${this.sessionMetadata.sessionId}\n`;
    contextString += `Active Topics: ${contextWindow.activeTopics.join(', ')}\n`;

    // Add relevant summaries
    if (contextWindow.relevantSummaries.length > 0) {
      contextString += '\nRelevant Context:\n';
      contextWindow.relevantSummaries.forEach(summary => {
        contextString += `- ${summary.summary}\n`;
      });
    }

    // Add recent messages if requested
    if (includeHistory && contextWindow.recentMessages.length > 0) {
      contextString += '\nRecent Messages:\n';
      contextWindow.recentMessages.slice(-5).forEach(msg => {
        contextString += `${msg.from}: ${msg.content.substring(0, 100)}...\n`;
      });
    }

    return contextString;
  }

  reset(): void {
    this.conversationHistory = [];
    this.contextSummaries = [];
    this.activeTopics.clear();
    this.userPreferences.clear();
    this.initializeSession();
    
    this.logger.info('ContextManager', 'Context manager reset');
  }
}
