"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Logger = void 0;
const chalk_1 = __importDefault(require("chalk"));
class Logger {
    constructor(debugMode = false) {
        this.logs = [];
        this.maxLogs = 1000;
        this.debugMode = false;
        this.debugMode = debugMode;
    }
    info(source, message, data) {
        this.log('info', source, message, data);
    }
    warn(source, message, data) {
        this.log('warn', source, message, data);
    }
    error(source, message, data) {
        this.log('error', source, message, data);
    }
    debug(source, message, data) {
        if (this.debugMode) {
            this.log('debug', source, message, data);
        }
    }
    log(level, source, message, data) {
        const entry = {
            timestamp: new Date(),
            level,
            source,
            message,
            data
        };
        this.logs.push(entry);
        // Maksimum log sayısını aş
        if (this.logs.length > this.maxLogs) {
            this.logs.shift();
        }
        // Console'a yazdır
        this.printToConsole(entry);
    }
    printToConsole(entry) {
        const timestamp = entry.timestamp.toLocaleTimeString();
        const source = `[${entry.source}]`;
        let coloredLevel;
        switch (entry.level) {
            case 'info':
                coloredLevel = chalk_1.default.blue('INFO');
                break;
            case 'warn':
                coloredLevel = chalk_1.default.yellow('WARN');
                break;
            case 'error':
                coloredLevel = chalk_1.default.red('ERROR');
                break;
            case 'debug':
                coloredLevel = chalk_1.default.gray('DEBUG');
                break;
        }
        const logLine = `${chalk_1.default.gray(timestamp)} ${coloredLevel} ${chalk_1.default.cyan(source)} ${entry.message}`;
        console.log(logLine);
        if (entry.data && this.debugMode) {
            console.log(chalk_1.default.gray('  Data:'), entry.data);
        }
    }
    getLogs(level, source, limit) {
        let filtered = this.logs;
        if (level) {
            filtered = filtered.filter(log => log.level === level);
        }
        if (source) {
            filtered = filtered.filter(log => log.source === source);
        }
        if (limit) {
            filtered = filtered.slice(-limit);
        }
        return filtered;
    }
    clear() {
        this.logs = [];
    }
    setDebugMode(enabled) {
        this.debugMode = enabled;
    }
}
exports.Logger = Logger;
//# sourceMappingURL=logger.js.map