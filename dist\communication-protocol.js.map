{"version": 3, "file": "communication-protocol.js", "sourceRoot": "", "sources": ["../src/communication-protocol.ts"], "names": [], "mappings": ";;;AAEA,+BAAoC;AAkCpC,MAAa,qBAAqB;IAYhC,YAAY,MAAc;QAXlB,iBAAY,GAAwC,IAAI,GAAG,EAAE,CAAC;QAC9D,yBAAoB,GAAsC,IAAI,GAAG,EAAE,CAAC;QACpE,kBAAa,GAAqB,EAAE,CAAC;QACrC,oBAAe,GAA0B,IAAI,GAAG,EAAE,CAAC;QAG3D,yBAAyB;QACR,oBAAe,GAAG,KAAK,CAAC,CAAC,aAAa;QACtC,mBAAc,GAAG,GAAG,CAAC;QACrB,8BAAyB,GAAG,EAAE,CAAC;QAG9C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,kBAAkB;QACxB,2BAA2B;QAC3B,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,uBAAuB;QAEjC,wBAAwB;QACxB,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAC9B,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,kBAAkB;QAE9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,oCAAoC,CAAC,CAAC;IAClF,CAAC;IAED,sBAAsB,CAAC,QAAgB,EAAE,OAAiB;QACxD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,kCAAkC,QAAQ,EAAE,CAAC,CAAC;IAC3F,CAAC;IAED,WAAW,CAAC,OAAuD;QACjE,MAAM,WAAW,GAAyB;YACxC,GAAG,OAAO;YACV,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,4BAA4B;QAC5B,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAEzE,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC/C,IAAI,KAAK,EAAE,CAAC;gBACV,yBAAyB;gBACzB,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;oBACxC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,wBAAwB;oBACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,0BAA0B,SAAS,2BAA2B,CAAC,CAAC;gBAC5G,CAAC;gBAED,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EACvC,sBAAsB,SAAS,KAAK,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,iCAAiC,SAAS,EAAE,CAAC,CAAC;YAC1F,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC,EAAE,CAAC;IACxB,CAAC;IAEO,KAAK,CAAC,oBAAoB;QAChC,KAAK,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YAClD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAS;YAEjC,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACnD,IAAI,CAAC,OAAO;gBAAE,SAAS;YAEvB,+BAA+B;YAC/B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;YAE9F,MAAM,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,mCAAmC;YAEjF,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE,CAAC;gBACxC,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;gBACxD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EACvC,+BAA+B,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,OAA6B,EAAE,OAAiB;QAC7F,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC;YAEvB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EACvC,wBAAwB,QAAQ,OAAO,YAAY,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QAE9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EACvC,+BAA+B,QAAQ,KAAK,KAAK,EAAE,CAAC,CAAC;YAEvD,qCAAqC;YACrC,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;gBACjE,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,QAAgB,EAAE,OAA6B;QAClE,MAAM,UAAU,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QAE3D,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC;YACpB,OAAO,CAAC,QAAQ,GAAG,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,UAAU,EAAE,CAAC;YAEvD,UAAU,CAAC,GAAG,EAAE;gBACd,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAC9C,IAAI,KAAK,EAAE,CAAC;oBACV,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;gBAClD,CAAC;YACH,CAAC,EAAE,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,sBAAsB;YAE7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EACtC,gCAAgC,QAAQ,aAAa,UAAU,GAAG,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,QAAgB;QACxC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;YACxB,KAAK,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;YACtB,KAAK,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;YACxB,KAAK,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC;YACrB,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAED,qBAAqB,CAAC,OAAgD;QACpE,MAAM,oBAAoB,GAAyB;YACjD,GAAG,OAAO;YACV,SAAS,EAAE,IAAA,SAAM,GAAE;SACpB,CAAC;QAEF,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;QAEpF,2BAA2B;QAC3B,MAAM,OAAO,GAAmD;YAC9D,IAAI,EAAE,uBAAuB;YAC7B,IAAI,EAAE,OAAO,CAAC,SAAS;YACvB,EAAE,EAAE,OAAO,CAAC,aAAa;YACzB,OAAO,EAAE,oBAAoB;YAC7B,QAAQ,EAAE,MAAM;YAChB,gBAAgB,EAAE,IAAI;YACtB,aAAa,EAAE,oBAAoB,CAAC,SAAS;SAC9C,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EACtC,4BAA4B,oBAAoB,CAAC,SAAS,SAAS,OAAO,CAAC,aAAa,CAAC,MAAM,UAAU,CAAC,CAAC;QAE7G,OAAO,oBAAoB,CAAC,SAAS,CAAC;IACxC,CAAC;IAED,sBAAsB,CAAC,SAAiB,EAAE,QAAgB,EAAE,QAAiB,EAAE,YAAuB;QACpG,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,4BAA4B,SAAS,EAAE,CAAC,CAAC;YACnF,OAAO;QACT,CAAC;QAED,MAAM,QAAQ,GAAG;YACf,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,YAAY,EAAE,YAAY,IAAI,EAAE;YAChC,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,6BAA6B;QAC7B,MAAM,OAAO,GAAmD;YAC9D,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,QAAQ;YACd,EAAE,EAAE,aAAa,CAAC,SAAS;YAC3B,OAAO,EAAE,EAAE,IAAI,EAAE,wBAAwB,EAAE,IAAI,EAAE,QAAQ,EAAE;YAC3D,QAAQ,EAAE,MAAM;YAChB,gBAAgB,EAAE,KAAK;YACvB,aAAa,EAAE,SAAS;SACzB,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAE1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EACvC,gCAAgC,QAAQ,IAAI,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,IAAI,SAAS,EAAE,CAAC,CAAC;IACnG,CAAC;IAED,cAAc,CAAC,KAAsC;QACnD,MAAM,cAAc,GAAmB;YACrC,GAAG,KAAK;YACR,OAAO,EAAE,IAAA,SAAM,GAAE;SAClB,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAExC,0BAA0B;QAC1B,IAAI,KAAK,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrC,MAAM,OAAO,GAAmD;gBAC9D,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,KAAK,CAAC,MAAM;gBAClB,EAAE,EAAE,KAAK,CAAC,eAAe;gBACzB,OAAO,EAAE,cAAc;gBACvB,QAAQ,EAAE,QAAQ;gBAClB,gBAAgB,EAAE,KAAK;aACxB,CAAC;YAEF,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EACtC,qBAAqB,KAAK,CAAC,aAAa,SAAS,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,eAAe,CAAC,MAAM,UAAU,CAAC,CAAC;QAE9G,OAAO,cAAc,CAAC,OAAO,CAAC;IAChC,CAAC;IAED,cAAc,CAAC,QAAgB,EAAE,KAAa,EAAE,cAAyB;QACvE,MAAM,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YAC9D,wCAAwC;YACxC,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC;gBACxE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,8CAA8C;YAC9C,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC9F,OAAO,KAAK,CAAC;YACf,CAAC;YAED,oDAAoD;YACpD,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;YACvC,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAExE,OAAO,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,iCAAiC;QACjC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9B,MAAM,cAAc,GAAG,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC;YACnD,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,GAAG;gBAAE,OAAO,cAAc,CAAC;YAE1D,yDAAyD;YACzD,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EACvC,wBAAwB,QAAQ,WAAW,iBAAiB,CAAC,MAAM,iBAAiB,CAAC,CAAC;QAExF,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,uBAAuB;IAC/D,CAAC;IAED,uBAAuB,CAAC,KAAa,EAAE,QAA8C;QACnF,MAAM,cAAc,GAAG,IAAA,SAAM,GAAE,CAAC;QAEhC,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAEtE,iDAAiD;QACjD,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAU,EAAE,KAAa,EAAE,EAAE;YAC5D,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;gBAChC,MAAM,OAAO,GAAmD;oBAC9D,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,QAAQ;oBACd,EAAE,EAAE,IAAI,CAAC,UAAU;oBACnB,OAAO,EAAE;wBACP,IAAI,EAAE,mBAAmB;wBACzB,cAAc;wBACd,KAAK,EAAE,KAAK;wBACZ,IAAI;wBACJ,YAAY,EAAE,KAAK,CAAC,YAAY;wBAChC,QAAQ;qBACT;oBACD,QAAQ,EAAE,MAAM;oBAChB,gBAAgB,EAAE,IAAI;oBACtB,aAAa,EAAE,cAAc;iBAC9B,CAAC;gBAEF,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EACtC,gCAAgC,cAAc,SAAS,KAAK,CAAC,MAAM,gBAAgB,QAAQ,WAAW,CAAC,CAAC;QAE1G,OAAO,cAAc,CAAC;IACxB,CAAC;IAEO,sBAAsB,CAAC,KAAa,EAAE,QAAgB;QAC5D,MAAM,IAAI,GAAG;YACX,cAAc,EAAE,IAAA,SAAM,GAAE;YACxB,QAAQ;YACR,MAAM,EAAE,EAAW;SACpB,CAAC;QAEF,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,UAAU;gBACb,gCAAgC;gBAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;oBACf,KAAK;oBACL,YAAY,EAAE,EAAE;iBACjB,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,YAAY;gBACf,8BAA8B;gBAC9B,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;oBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;wBACf,KAAK,EAAE,CAAC,IAAI,CAAC;wBACb,YAAY,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;qBACrD,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,QAAQ;gBACX,+CAA+C;gBAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAC7C,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;oBAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;wBACf,KAAK,EAAE,KAAK;wBACZ,YAAY,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;qBAChE,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM;QACV,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,iBAAiB,CAAC,KAAa;QACrC,0CAA0C;QAC1C,uEAAuE;QACvE,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,YAAY,GAAW,EAAE,CAAC;QAE9B,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;gBAC7E,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC1B,YAAY,GAAG,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,eAAe,CAAC,KAAW,EAAE,KAAW;QAC9C,+CAA+C;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QAC9C,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;QAE9C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACjD,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CACxC,CAAC;QAEF,OAAO,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;IAChC,CAAC;IAEO,oBAAoB;QAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,yBAAyB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAElF,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YACzD,wCAAwC;YACxC,MAAM,mBAAmB,GAAG,SAAS,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,GAAG,CAAC,UAAU,GAAG,mBAAmB,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC9D,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,cAAc,YAAY,wBAAwB,CAAC,CAAC;QACjG,CAAC;IACH,CAAC;IAED,qBAAqB;QACnB,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;aACzD,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAEjD,OAAO;YACL,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;YACpC,mBAAmB,EAAE,aAAa;YAClC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI;YACpD,iBAAiB,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM;YAC5C,kBAAkB,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI;SAC9C,CAAC;IACJ,CAAC;IAED,uBAAuB;QACrB,OAAO;YACL,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;YACpD,oBAAoB,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC;YACnE,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,0BAA0B;YACxE,UAAU,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACxC,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;IACJ,CAAC;CACF;AAzZD,sDAyZC"}