import { MasterA<PERSON> } from './master-ai';
import { Logger } from './logger';
export declare class TerminalUI {
    private screen;
    private masterAI;
    private logger;
    private state;
    private boxes;
    constructor(masterAI: MasterAI, logger: Logger);
    private initializeScreen;
    private setupLayout;
    private setupEventHandlers;
    private handleUserInput;
    private addToChatPanel;
    private getMessageIcon;
    private updateWorkerStatus;
    private getSystemHealthStatus;
    private formatResponseTime;
    private formatErrorRate;
    private getTaskStatusIcon;
    private updatePerformanceIndicator;
    private updateLogPanel;
    private getLogLevelIcon;
    private getLogLevelColor;
    private showHelp;
    private showPerformanceReport;
    private getGradeIcon;
    private formatPercentage;
    private formatRating;
    private showSystemStatistics;
    private calculateSuccessRate;
    private formatTopics;
    private formatImportance;
    private formatUptime;
    private getMemoryUsage;
    private getCPULoad;
    private getNetworkStatus;
    start(): Promise<void>;
}
//# sourceMappingURL=terminal-ui.d.ts.map