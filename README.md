# Terminal AI Orchestrator (TAO) - Advanced Edition

Terminal içerisinde yaşayan, gelişmiş çoklu AI modeli orkestrasyon sistemi. Master-Worker mimarisi ile çalışır ve OpenAI uyumlu providerları destekler.

## 🚀 <PERSON><PERSON> Özel<PERSON>ler (v2.0)

### 🧠 Gelişmiş Sistem Promptları
- **Bağlamsal Promptlar**: Her AI için özelleştirilmiş, detaylı sistem promptları
- **Adaptif <PERSON>**: Görev tipine göre dinamik prompt optimizasyonu
- **Profesyonel Kimlik**: Her AI'ın kendine özgü uzmanlık alanı ve çalışma metodolojisi

### 🎯 Akıllı Görev Dağıtım Sistemi
- **Dinamik Analiz**: Görev karmaşıklığı ve gereksinimlerinin otomatik analizi
- **Yetenek Eşleştirme**: AI'ların uzmanlık alanlarına göre optimal görev ataması
- **<PERSON><PERSON>k Dengeleme**: Worker AI'ların mevcut yüklerine göre akıllı dağıtım
- **Performans Tabanlı**: Geçmiş performansa dayalı görev önceliklendirme

### 🧩 Bellek ve Bağlam Yönetimi
- **Konuşma Geçmişi**: Akıllı özet oluşturma ve bağlam koruma
- **Konu Takibi**: Aktif konuların otomatik tespiti ve yönetimi
- **Kullanıcı Tercihleri**: Kişiselleştirilmiş deneyim için tercih saklama
- **Oturum Yönetimi**: Uzun süreli konuşma sürekliliği

### 📊 Performans İzleme ve Optimizasyon
- **Real-time Metrikler**: Anlık performans izleme ve raporlama
- **Kalite Skorları**: Kullanıcı geri bildirimlerine dayalı kalite değerlendirmesi
- **Verimlilik Analizi**: Görev tamamlama süreleri ve başarı oranları
- **Otomatik Optimizasyon**: Performans verilerine dayalı sistem iyileştirmeleri

### 🔄 Gelişmiş İletişim Protokolü
- **AI'lar Arası İşbirliği**: Koordineli görev yürütme ve bilgi paylaşımı
- **Bilgi Tabanı**: Öğrenilen bilgilerin saklanması ve paylaşılması
- **Mesaj Kuyruğu**: Öncelikli mesaj işleme ve güvenilir iletim
- **Koordinasyon Stratejileri**: Paralel, sıralı ve hibrit çalışma modları

## 📋 Temel Özellikler

- **Master AI**: Diğer AI modellerini yöneten ana kontrol birimi
- **Worker AI'lar**: Özelleşmiş görevleri yerine getiren yardımcı modeller (Code, Research, Creative)
- **Real-time İletişim**: AI modelleri arası sürekli bilgi akışı
- **OpenAI Uyumlu**: Herhangi bir OpenAI compatible provider kullanabilir
- **Konfigürasyon**: Kullanıcı tanımlı model ayarları
- **Terminal UI**: Gelişmiş interaktif terminal arayüzü

## 🏗️ Gelişmiş Mimari

```
┌─────────────────────────────────────────────────────────┐
│                    Master AI Orchestrator               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │
│  │Task         │ │Context      │ │Performance          │ │
│  │Orchestrator │ │Manager      │ │Monitor              │ │
│  └─────────────┘ └─────────────┘ └─────────────────────┘ │
│  ┌─────────────────────────────────────────────────────┐ │
│  │         Communication Protocol                      │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────┬───────────────────────────────────────┘
                  │ Intelligent Task Distribution
        ┌─────────┼─────────┐
        │         │         │
┌───────▼───┐ ┌───▼───┐ ┌───▼───┐
│Code       │ │Research│ │Creative│
│Specialist │ │Assistant│ │Specialist│
│           │ │        │ │        │
│• Analysis │ │• Info  │ │• Writing│
│• Debug    │ │• Facts │ │• Ideas  │
│• Optimize │ │• Report│ │• Content│
└───────────┘ └────────┘ └────────┘
      ↕             ↕         ↕
   ┌─────────────────────────────────┐
   │    Inter-AI Communication      │
   │    & Knowledge Sharing          │
   └─────────────────────────────────┘
```

## 🛠️ Kurulum

```bash
# Bağımlılıkları yükle
npm install

# Projeyi derle
npm run build

# Sistemi başlat
npm start

# Geliştirme modu
npm run dev
```

## ⚙️ Konfigürasyon

`config/models.json` dosyasını düzenleyerek AI modellerinizi yapılandırın:

```json
{
  "master": {
    "name": "Master Orchestrator",
    "provider": {
      "baseURL": "https://api.openai.com/v1",
      "apiKey": "your-api-key-here",
      "model": "gpt-4"
    },
    "systemPrompt": "Gelişmiş Master AI sistem promptu...",
    "capabilities": ["orchestration", "task_distribution", "context_management"]
  },
  "workers": [
    {
      "id": "worker_1",
      "name": "Code Specialist",
      "specialization": "code_analysis",
      "systemPrompt": "Gelişmiş kod uzmanı sistem promptu...",
      "capabilities": ["code_analysis", "debugging", "optimization"]
    }
  ]
}
```

## 🎮 Kullanım

### 🎮 Modern Terminal UI Kontrolleri
- **F1**: 📖 Interactive help guide
- **F2**: 📊 System dashboard refresh
- **F3**: 🐛 Debug logs toggle
- **F4**: ⚡ Performance analytics (YENİ!)
- **F5**: 📈 System statistics (YENİ!)
- **ESC**: 🚪 Graceful exit

### 🎨 Modern UI Features
- **Visual Dashboard**: Modern card-based system overview
- **Chat Bubbles**: Elegant conversation interface
- **Activity Monitor**: Real-time log visualization
- **Performance Indicators**: Live system health metrics
- **Color-coded Status**: Intuitive visual feedback
- **Progress Bars**: Task completion visualization

### 🚀 Advanced Capabilities
- **🎯 Smart Task Distribution**: AI automatically selects optimal workers
- **📊 Real-time Monitoring**: Live performance metrics and analytics
- **🧠 Context Management**: Intelligent conversation history and topic tracking
- **🤝 AI Collaboration**: Coordinated multi-AI task execution
- **⚡ Performance Optimization**: Adaptive system tuning and optimization