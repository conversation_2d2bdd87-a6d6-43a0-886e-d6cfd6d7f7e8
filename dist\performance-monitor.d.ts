import { Logger } from './logger';
export interface PerformanceMetrics {
    workerId: string;
    workerName: string;
    totalTasks: number;
    completedTasks: number;
    failedTasks: number;
    averageResponseTime: number;
    successRate: number;
    efficiency: number;
    qualityScore: number;
    lastActive: Date;
    specialization: string;
}
export interface SystemMetrics {
    totalRequests: number;
    averageSystemResponseTime: number;
    systemUptime: number;
    activeWorkers: number;
    taskThroughput: number;
    errorRate: number;
    resourceUtilization: number;
    userSatisfactionScore: number;
}
export interface TaskMetrics {
    taskId: string;
    workerId: string;
    startTime: Date;
    endTime?: Date;
    responseTime?: number;
    success: boolean;
    complexity: number;
    userFeedback?: number;
    retryCount: number;
}
export declare class PerformanceMonitor {
    private workerMetrics;
    private taskMetrics;
    private systemStartTime;
    private logger;
    private readonly RESPONSE_TIME_THRESHOLD;
    private readonly SUCCESS_RATE_THRESHOLD;
    private readonly EFFICIENCY_THRESHOLD;
    private readonly QUALITY_THRESHOLD;
    constructor(logger: Logger);
    private startPerformanceTracking;
    registerWorker(workerId: string, workerName: string, specialization: string): void;
    startTask(taskId: string, workerId: string, complexity: number): void;
    completeTask(taskId: string, success: boolean, userFeedback?: number): void;
    retryTask(taskId: string): void;
    getWorkerMetrics(workerId: string): PerformanceMetrics | undefined;
    getAllWorkerMetrics(): PerformanceMetrics[];
    getSystemMetrics(): SystemMetrics;
    private calculateResourceUtilization;
    private analyzePerformance;
    private cleanupOldMetrics;
    getPerformanceReport(): any;
    private calculateSystemGrade;
    private calculateWorkerGrade;
    private generateRecommendations;
    exportMetrics(): any;
}
//# sourceMappingURL=performance-monitor.d.ts.map