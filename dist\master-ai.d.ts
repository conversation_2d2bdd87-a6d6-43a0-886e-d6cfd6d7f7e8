import { <PERSON><PERSON><PERSON> } from './worker-ai';
import { Task, AIModel, ConversationContext } from './types';
import { Logger } from './logger';
export declare class MasterAI {
    private aiService;
    private workers;
    private context;
    private logger;
    private activeTasks;
    private taskOrchestrator;
    private contextManager;
    private performanceMonitor;
    private communicationProtocol;
    constructor(model: AIModel, workers: AIModel[], logger: Logger);
    processUserInput(input: string): Promise<string>;
    private analyzeAndDistributeTasks;
    private createTask;
    receiveWorkerResponse(workerId: string, taskId: string, result: string): Promise<void>;
    private handleWorkerMessage;
    private handleCollaborationRequest;
    private handleKnowledgeShare;
    private handleStatusUpdate;
    private buildContextString;
    getWorkers(): WorkerAI[];
    getActiveTasks(): Task[];
    getContext(): ConversationContext;
    broadcastToWorkers(message: string): Promise<void>;
    getPerformanceReport(): any;
    getSystemStatistics(): any;
    updateUserPreference(key: string, value: any): void;
    getUserPreference(key: string): any;
    initiateWorkerCollaboration(objective: string, workerIds: string[]): Promise<string>;
    shareKnowledgeWithWorkers(knowledge: any, knowledgeType: string, relevantWorkers: string[]): void;
    resetContext(): void;
}
//# sourceMappingURL=master-ai.d.ts.map