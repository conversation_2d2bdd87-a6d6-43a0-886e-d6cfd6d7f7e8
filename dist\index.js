#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalAIOrchestrator = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const commander_1 = require("commander");
const inquirer_1 = __importDefault(require("inquirer"));
const chalk_1 = __importDefault(require("chalk"));
const master_ai_1 = require("./master-ai");
const logger_1 = require("./logger");
const terminal_ui_1 = require("./terminal-ui");
class TerminalAIOrchestrator {
    constructor() {
        this.isRunning = false;
        this.logger = new logger_1.Logger(false);
        this.loadConfiguration();
        this.initializeSystem();
    }
    loadConfiguration() {
        const configPath = (0, path_1.join)(process.cwd(), 'config', 'models.json');
        if (!(0, fs_1.existsSync)(configPath)) {
            this.logger.error('System', `Konfigürasyon dosyası bulunamadı: ${configPath}`);
            process.exit(1);
        }
        try {
            const configData = (0, fs_1.readFileSync)(configPath, 'utf-8');
            this.config = JSON.parse(configData);
            this.logger.info('System', 'Konfigürasyon yüklendi');
        }
        catch (error) {
            this.logger.error('System', `Konfigürasyon yükleme hatası: ${error}`);
            process.exit(1);
        }
    }
    initializeSystem() {
        try {
            // Worker ID'lerini ata
            this.config.workers.forEach((worker, index) => {
                if (!worker.id) {
                    worker.id = `worker_${index + 1}`;
                }
            });
            this.masterAI = new master_ai_1.MasterAI(this.config.master, this.config.workers, this.logger);
            this.ui = new terminal_ui_1.TerminalUI(this.masterAI, this.logger);
            this.logger.info('System', 'Terminal AI Orchestrator başlatıldı');
        }
        catch (error) {
            this.logger.error('System', `Sistem başlatma hatası: ${error}`);
            process.exit(1);
        }
    }
    async start() {
        this.isRunning = true;
        console.log(chalk_1.default.cyan.bold('\n🤖 Terminal AI Orchestrator'));
        console.log(chalk_1.default.gray('Çoklu AI modeli orkestrasyon sistemi\n'));
        this.showSystemInfo();
        await this.ui.start();
    }
    showSystemInfo() {
        console.log(chalk_1.default.yellow('📋 Sistem Bilgileri:'));
        console.log(`   Master AI: ${chalk_1.default.green(this.config.master.name)}`);
        console.log(`   Worker AI'lar: ${this.config.workers.length} adet`);
        this.config.workers.forEach(worker => {
            console.log(`   - ${chalk_1.default.blue(worker.name)} (${worker.specialization})`);
        });
        console.log(`   İletişim: ${this.config.communication.enableInterWorkerCommunication ? chalk_1.default.green('Aktif') : chalk_1.default.red('Pasif')}`);
        console.log('');
    }
    async interactiveMode() {
        while (this.isRunning) {
            try {
                const { input } = await inquirer_1.default.prompt([
                    {
                        type: 'input',
                        name: 'input',
                        message: chalk_1.default.cyan('Siz:'),
                        prefix: '💬'
                    }
                ]);
                if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'çıkış') {
                    this.isRunning = false;
                    console.log(chalk_1.default.yellow('Görüşmek üzere! 👋'));
                    break;
                }
                if (input.toLowerCase() === 'help' || input.toLowerCase() === 'yardım') {
                    this.showHelp();
                    continue;
                }
                if (input.toLowerCase() === 'status' || input.toLowerCase() === 'durum') {
                    this.showStatus();
                    continue;
                }
                console.log(chalk_1.default.gray('🤔 İşleniyor...'));
                const response = await this.masterAI.processUserInput(input);
                console.log(chalk_1.default.green(`🤖 Master AI: ${response}\n`));
            }
            catch (error) {
                console.log(chalk_1.default.red(`❌ Hata: ${error}\n`));
            }
        }
    }
    showHelp() {
        console.log(chalk_1.default.yellow('\n📖 Yardım:'));
        console.log('  help/yardım  - Bu yardım mesajını göster');
        console.log('  status/durum - Sistem durumunu göster');
        console.log('  exit/çıkış   - Uygulamadan çık');
        console.log('  Diğer tüm girişler Master AI\'a gönderilir\n');
    }
    showStatus() {
        console.log(chalk_1.default.yellow('\n📊 Sistem Durumu:'));
        const workers = this.masterAI.getWorkers();
        workers.forEach(worker => {
            const status = worker.isAvailable() ? chalk_1.default.green('Müsait') : chalk_1.default.yellow('Meşgul');
            console.log(`  ${worker.getName()}: ${status} - ${worker.getStatus()}`);
        });
        const activeTasks = this.masterAI.getActiveTasks();
        console.log(`  Aktif Görevler: ${activeTasks.length}`);
        console.log('');
    }
}
exports.TerminalAIOrchestrator = TerminalAIOrchestrator;
// CLI setup
const program = new commander_1.Command();
program
    .name('tao')
    .description('Terminal AI Orchestrator - Çoklu AI modeli orkestrasyon sistemi')
    .version('1.0.0');
program
    .command('start')
    .description('Sistemi başlat')
    .option('-d, --debug', 'Debug modunu etkinleştir')
    .option('-i, --interactive', 'İnteraktif mod (varsayılan)')
    .action(async (options) => {
    const tao = new TerminalAIOrchestrator();
    if (options.debug) {
        tao.logger.setDebugMode(true);
    }
    await tao.start();
    if (options.interactive !== false) {
        await tao.interactiveMode();
    }
});
program
    .command('config')
    .description('Konfigürasyonu göster')
    .action(() => {
    const configPath = (0, path_1.join)(process.cwd(), 'config', 'models.json');
    if ((0, fs_1.existsSync)(configPath)) {
        const config = JSON.parse((0, fs_1.readFileSync)(configPath, 'utf-8'));
        console.log(JSON.stringify(config, null, 2));
    }
    else {
        console.log(chalk_1.default.red('Konfigürasyon dosyası bulunamadı'));
    }
});
// Ana program
if (require.main === module) {
    program.parse();
}
//# sourceMappingURL=index.js.map