import { v4 as uuidv4 } from 'uuid';
import { AIService } from './ai-service';
import { Message, Task, AIModel } from './types';
import { Logger } from './logger';

export class WorkerAI {
  private aiService: AIService;
  private model: AIModel;
  private masterAI: any; // MasterAI referansı (circular dependency önlemek için any)
  private logger: Logger;
  private messageHistory: Message[] = [];
  private currentTask?: Task;

  constructor(model: AIModel, masterAI: any, logger: Logger) {
    this.model = model;
    this.aiService = new AIService(model);
    this.masterAI = masterAI;
    this.logger = logger;
    
    this.logger.info('WorkerAI', `Worker AI başlatıldı: ${model.name} (${model.specialization})`);
  }

  async assignTask(task: Task): Promise<void> {
    this.currentTask = task;
    task.status = 'in_progress';
    
    this.logger.info('WorkerAI', `Görev atandı: ${task.id} - ${task.description}`);

    try {
      const taskMessage: Message = {
        id: uuidv4(),
        from: 'master',
        to: this.model.id!,
        content: `Görev: ${task.description}`,
        timestamp: new Date(),
        type: 'system'
      };

      this.messageHistory.push(taskMessage);

      const response = await this.aiService.sendMessage(
        this.messageHistory,
        this.buildTaskContext(task)
      );

      const result = response.content;
      
      // Sonucu master AI'a gönder
      await this.masterAI.receiveWorkerResponse(this.model.id!, task.id, result);
      
      this.currentTask = undefined;
      
      this.logger.info('WorkerAI', `Görev tamamlandı: ${task.id}`);
      
    } catch (error) {
      task.status = 'failed';
      this.logger.error('WorkerAI', `Görev başarısız: ${task.id} - ${error}`);
      throw error;
    }
  }

  async receiveMessage(content: string, from: string): Promise<string> {
    const message: Message = {
      id: uuidv4(),
      from,
      to: this.model.id!,
      content,
      timestamp: new Date(),
      type: 'inter_ai'
    };

    this.messageHistory.push(message);
    
    try {
      const response = await this.aiService.sendMessage(this.messageHistory);
      
      const responseMessage: Message = {
        id: uuidv4(),
        from: this.model.id!,
        to: from,
        content: response.content,
        timestamp: new Date(),
        type: 'ai_response'
      };
      
      this.messageHistory.push(responseMessage);
      
      return response.content;
    } catch (error) {
      this.logger.error('WorkerAI', `Mesaj işleme hatası: ${error}`);
      return `Hata: ${error}`;
    }
  }

  private buildTaskContext(task: Task): string {
    return `
Görev ID: ${task.id}
Görev Açıklaması: ${task.description}
Uzmanlık Alanım: ${this.model.specialization}
Yeteneklerim: ${this.model.capabilities.join(', ')}
Görev Durumu: ${task.status}
Oluşturulma Zamanı: ${task.createdAt.toISOString()}

Bu görevi yeteneklerim dahilinde en iyi şekilde tamamlamalıyım.
    `.trim();
  }

  getId(): string {
    return this.model.id!;
  }

  getName(): string {
    return this.model.name;
  }

  getSpecialization(): string {
    return this.model.specialization || 'general';
  }

  getCapabilities(): string[] {
    return this.model.capabilities;
  }

  getCurrentTask(): Task | undefined {
    return this.currentTask;
  }

  getMessageHistory(): Message[] {
    return this.messageHistory;
  }

  isAvailable(): boolean {
    return !this.currentTask || this.currentTask.status === 'completed';
  }

  getStatus(): string {
    if (this.currentTask) {
      return `Görev: ${this.currentTask.description} (${this.currentTask.status})`;
    }
    return 'Beklemede';
  }

  getModel(): AIModel {
    return this.model;
  }
}