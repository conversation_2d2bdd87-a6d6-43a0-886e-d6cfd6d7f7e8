import { AIModel, AIResponse, Message } from './types';
export declare class AIService {
    private client;
    private model;
    constructor(model: AIModel);
    sendMessage(messages: Message[], context?: string): Promise<AIResponse>;
    private formatMessages;
    private buildSystemPrompt;
    getModelInfo(): AIModel;
    updateModel(updates: Partial<AIModel>): void;
}
//# sourceMappingURL=ai-service.d.ts.map