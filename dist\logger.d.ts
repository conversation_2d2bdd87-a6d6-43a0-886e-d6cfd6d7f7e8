import { LogEntry } from './types';
export declare class Logger {
    private logs;
    private maxLogs;
    private debugMode;
    constructor(debugMode?: boolean);
    info(source: string, message: string, data?: any): void;
    warn(source: string, message: string, data?: any): void;
    error(source: string, message: string, data?: any): void;
    debug(source: string, message: string, data?: any): void;
    private log;
    private printToConsole;
    getLogs(level?: LogEntry['level'], source?: string, limit?: number): LogEntry[];
    clear(): void;
    setDebugMode(enabled: boolean): void;
}
//# sourceMappingURL=logger.d.ts.map