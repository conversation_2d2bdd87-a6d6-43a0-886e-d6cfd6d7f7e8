import { Task, AIModel, Message } from './types';
import { Logger } from './logger';

export interface PerformanceMetrics {
  workerId: string;
  workerName: string;
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageResponseTime: number;
  successRate: number;
  efficiency: number; // Tasks per minute
  qualityScore: number; // 0-1 based on user feedback
  lastActive: Date;
  specialization: string;
}

export interface SystemMetrics {
  totalRequests: number;
  averageSystemResponseTime: number;
  systemUptime: number;
  activeWorkers: number;
  taskThroughput: number; // Tasks per hour
  errorRate: number;
  resourceUtilization: number;
  userSatisfactionScore: number;
}

export interface TaskMetrics {
  taskId: string;
  workerId: string;
  startTime: Date;
  endTime?: Date;
  responseTime?: number;
  success: boolean;
  complexity: number;
  userFeedback?: number; // 1-5 rating
  retryCount: number;
}

export class PerformanceMonitor {
  private workerMetrics: Map<string, PerformanceMetrics> = new Map();
  private taskMetrics: TaskMetrics[] = [];
  private systemStartTime: Date;
  private logger: Logger;
  
  // Performance thresholds
  private readonly RESPONSE_TIME_THRESHOLD = 10000; // 10 seconds
  private readonly SUCCESS_RATE_THRESHOLD = 0.85; // 85%
  private readonly EFFICIENCY_THRESHOLD = 5; // 5 tasks per minute
  private readonly QUALITY_THRESHOLD = 3.5; // 3.5/5 rating

  constructor(logger: Logger) {
    this.logger = logger;
    this.systemStartTime = new Date();
    this.startPerformanceTracking();
  }

  private startPerformanceTracking(): void {
    // Periodic performance analysis
    setInterval(() => {
      this.analyzePerformance();
    }, 60000); // Every minute

    // Cleanup old metrics
    setInterval(() => {
      this.cleanupOldMetrics();
    }, 300000); // Every 5 minutes

    this.logger.info('PerformanceMonitor', 'Performance tracking started');
  }

  registerWorker(workerId: string, workerName: string, specialization: string): void {
    const metrics: PerformanceMetrics = {
      workerId,
      workerName,
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      averageResponseTime: 0,
      successRate: 1.0,
      efficiency: 0,
      qualityScore: 5.0,
      lastActive: new Date(),
      specialization
    };

    this.workerMetrics.set(workerId, metrics);
    this.logger.info('PerformanceMonitor', `Worker registered: ${workerName}`);
  }

  startTask(taskId: string, workerId: string, complexity: number): void {
    const taskMetric: TaskMetrics = {
      taskId,
      workerId,
      startTime: new Date(),
      success: false,
      complexity,
      retryCount: 0
    };

    this.taskMetrics.push(taskMetric);
    
    // Update worker metrics
    const workerMetrics = this.workerMetrics.get(workerId);
    if (workerMetrics) {
      workerMetrics.totalTasks++;
      workerMetrics.lastActive = new Date();
    }

    this.logger.debug('PerformanceMonitor', `Task started: ${taskId} for worker ${workerId}`);
  }

  completeTask(taskId: string, success: boolean, userFeedback?: number): void {
    const taskMetric = this.taskMetrics.find(t => t.taskId === taskId);
    if (!taskMetric) {
      this.logger.warn('PerformanceMonitor', `Task metric not found: ${taskId}`);
      return;
    }

    taskMetric.endTime = new Date();
    taskMetric.responseTime = taskMetric.endTime.getTime() - taskMetric.startTime.getTime();
    taskMetric.success = success;
    taskMetric.userFeedback = userFeedback;

    // Update worker metrics
    const workerMetrics = this.workerMetrics.get(taskMetric.workerId);
    if (workerMetrics) {
      if (success) {
        workerMetrics.completedTasks++;
      } else {
        workerMetrics.failedTasks++;
      }

      // Update success rate
      workerMetrics.successRate = workerMetrics.completedTasks / workerMetrics.totalTasks;

      // Update average response time
      const completedTasks = this.taskMetrics.filter(t => 
        t.workerId === taskMetric.workerId && t.success && t.responseTime
      );
      
      if (completedTasks.length > 0) {
        workerMetrics.averageResponseTime = completedTasks.reduce((sum, t) => 
          sum + (t.responseTime || 0), 0) / completedTasks.length;
      }

      // Update efficiency (tasks per minute)
      const timeWindow = 60 * 1000; // 1 minute
      const recentTasks = this.taskMetrics.filter(t => 
        t.workerId === taskMetric.workerId && 
        t.endTime && 
        (Date.now() - t.endTime.getTime()) < timeWindow
      );
      workerMetrics.efficiency = recentTasks.length;

      // Update quality score
      const tasksWithFeedback = this.taskMetrics.filter(t => 
        t.workerId === taskMetric.workerId && t.userFeedback
      );
      
      if (tasksWithFeedback.length > 0) {
        workerMetrics.qualityScore = tasksWithFeedback.reduce((sum, t) => 
          sum + (t.userFeedback || 0), 0) / tasksWithFeedback.length;
      }

      workerMetrics.lastActive = new Date();
    }

    this.logger.debug('PerformanceMonitor', 
      `Task completed: ${taskId}, success: ${success}, time: ${taskMetric.responseTime}ms`);
  }

  retryTask(taskId: string): void {
    const taskMetric = this.taskMetrics.find(t => t.taskId === taskId);
    if (taskMetric) {
      taskMetric.retryCount++;
      this.logger.debug('PerformanceMonitor', `Task retry: ${taskId}, count: ${taskMetric.retryCount}`);
    }
  }

  getWorkerMetrics(workerId: string): PerformanceMetrics | undefined {
    return this.workerMetrics.get(workerId);
  }

  getAllWorkerMetrics(): PerformanceMetrics[] {
    return Array.from(this.workerMetrics.values());
  }

  getSystemMetrics(): SystemMetrics {
    const allWorkers = Array.from(this.workerMetrics.values());
    const allTasks = this.taskMetrics;
    const completedTasks = allTasks.filter(t => t.success);
    const failedTasks = allTasks.filter(t => !t.success && t.endTime);
    
    const uptime = Date.now() - this.systemStartTime.getTime();
    const uptimeHours = uptime / (1000 * 60 * 60);
    
    const totalResponseTime = completedTasks.reduce((sum, t) => sum + (t.responseTime || 0), 0);
    const averageResponseTime = completedTasks.length > 0 ? totalResponseTime / completedTasks.length : 0;
    
    const taskThroughput = uptimeHours > 0 ? completedTasks.length / uptimeHours : 0;
    const errorRate = allTasks.length > 0 ? failedTasks.length / allTasks.length : 0;
    
    const activeWorkers = allWorkers.filter(w => {
      const timeSinceActive = Date.now() - w.lastActive.getTime();
      return timeSinceActive < 300000; // Active in last 5 minutes
    }).length;

    const tasksWithFeedback = allTasks.filter(t => t.userFeedback);
    const userSatisfactionScore = tasksWithFeedback.length > 0 
      ? tasksWithFeedback.reduce((sum, t) => sum + (t.userFeedback || 0), 0) / tasksWithFeedback.length
      : 0;

    return {
      totalRequests: allTasks.length,
      averageSystemResponseTime: averageResponseTime,
      systemUptime: uptime,
      activeWorkers,
      taskThroughput,
      errorRate,
      resourceUtilization: this.calculateResourceUtilization(),
      userSatisfactionScore
    };
  }

  private calculateResourceUtilization(): number {
    const allWorkers = Array.from(this.workerMetrics.values());
    if (allWorkers.length === 0) return 0;

    const utilizationSum = allWorkers.reduce((sum, worker) => {
      // Calculate utilization based on efficiency and success rate
      const maxEfficiency = 10; // Assume max 10 tasks per minute
      const normalizedEfficiency = Math.min(worker.efficiency / maxEfficiency, 1);
      const utilization = normalizedEfficiency * worker.successRate;
      return sum + utilization;
    }, 0);

    return utilizationSum / allWorkers.length;
  }

  private analyzePerformance(): void {
    const systemMetrics = this.getSystemMetrics();
    const workerMetrics = this.getAllWorkerMetrics();

    // System-level analysis
    if (systemMetrics.averageSystemResponseTime > this.RESPONSE_TIME_THRESHOLD) {
      this.logger.warn('PerformanceMonitor', 
        `System response time high: ${systemMetrics.averageSystemResponseTime}ms`);
    }

    if (systemMetrics.errorRate > (1 - this.SUCCESS_RATE_THRESHOLD)) {
      this.logger.warn('PerformanceMonitor', 
        `System error rate high: ${(systemMetrics.errorRate * 100).toFixed(1)}%`);
    }

    // Worker-level analysis
    workerMetrics.forEach(worker => {
      const issues: string[] = [];

      if (worker.averageResponseTime > this.RESPONSE_TIME_THRESHOLD) {
        issues.push(`slow response (${worker.averageResponseTime}ms)`);
      }

      if (worker.successRate < this.SUCCESS_RATE_THRESHOLD) {
        issues.push(`low success rate (${(worker.successRate * 100).toFixed(1)}%)`);
      }

      if (worker.efficiency < this.EFFICIENCY_THRESHOLD) {
        issues.push(`low efficiency (${worker.efficiency} tasks/min)`);
      }

      if (worker.qualityScore < this.QUALITY_THRESHOLD) {
        issues.push(`low quality score (${worker.qualityScore.toFixed(1)}/5)`);
      }

      if (issues.length > 0) {
        this.logger.warn('PerformanceMonitor', 
          `Worker ${worker.workerName} performance issues: ${issues.join(', ')}`);
      }
    });

    // Log performance summary
    this.logger.info('PerformanceMonitor', 
      `Performance Summary - Active Workers: ${systemMetrics.activeWorkers}, ` +
      `Avg Response: ${systemMetrics.averageSystemResponseTime.toFixed(0)}ms, ` +
      `Throughput: ${systemMetrics.taskThroughput.toFixed(1)} tasks/hour, ` +
      `Error Rate: ${(systemMetrics.errorRate * 100).toFixed(1)}%`);
  }

  private cleanupOldMetrics(): void {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
    
    const initialCount = this.taskMetrics.length;
    this.taskMetrics = this.taskMetrics.filter(t => 
      t.startTime.getTime() > cutoffTime
    );
    
    const removedCount = initialCount - this.taskMetrics.length;
    if (removedCount > 0) {
      this.logger.debug('PerformanceMonitor', `Cleaned up ${removedCount} old task metrics`);
    }
  }

  getPerformanceReport(): any {
    const systemMetrics = this.getSystemMetrics();
    const workerMetrics = this.getAllWorkerMetrics();
    
    // Calculate performance grades
    const systemGrade = this.calculateSystemGrade(systemMetrics);
    const workerGrades = workerMetrics.map(worker => ({
      ...worker,
      grade: this.calculateWorkerGrade(worker)
    }));

    return {
      timestamp: new Date(),
      systemMetrics: {
        ...systemMetrics,
        grade: systemGrade
      },
      workerMetrics: workerGrades,
      recommendations: this.generateRecommendations(systemMetrics, workerMetrics)
    };
  }

  private calculateSystemGrade(metrics: SystemMetrics): string {
    let score = 100;
    
    if (metrics.averageSystemResponseTime > this.RESPONSE_TIME_THRESHOLD) score -= 20;
    if (metrics.errorRate > 0.15) score -= 25;
    if (metrics.resourceUtilization < 0.5) score -= 15;
    if (metrics.userSatisfactionScore < 3.5) score -= 20;
    if (metrics.taskThroughput < 1) score -= 10;

    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }

  private calculateWorkerGrade(worker: PerformanceMetrics): string {
    let score = 100;
    
    if (worker.averageResponseTime > this.RESPONSE_TIME_THRESHOLD) score -= 25;
    if (worker.successRate < this.SUCCESS_RATE_THRESHOLD) score -= 30;
    if (worker.efficiency < this.EFFICIENCY_THRESHOLD) score -= 20;
    if (worker.qualityScore < this.QUALITY_THRESHOLD) score -= 25;

    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }

  private generateRecommendations(systemMetrics: SystemMetrics, workerMetrics: PerformanceMetrics[]): string[] {
    const recommendations: string[] = [];

    // System recommendations
    if (systemMetrics.averageSystemResponseTime > this.RESPONSE_TIME_THRESHOLD) {
      recommendations.push('Consider optimizing task distribution or adding more workers');
    }

    if (systemMetrics.errorRate > 0.15) {
      recommendations.push('Investigate and fix recurring errors in the system');
    }

    if (systemMetrics.resourceUtilization < 0.5) {
      recommendations.push('System is underutilized - consider reducing worker count or increasing task load');
    }

    // Worker recommendations
    const underperformingWorkers = workerMetrics.filter(w => 
      w.successRate < this.SUCCESS_RATE_THRESHOLD || 
      w.averageResponseTime > this.RESPONSE_TIME_THRESHOLD
    );

    if (underperformingWorkers.length > 0) {
      recommendations.push(`Review configuration for workers: ${underperformingWorkers.map(w => w.workerName).join(', ')}`);
    }

    const lowQualityWorkers = workerMetrics.filter(w => w.qualityScore < this.QUALITY_THRESHOLD);
    if (lowQualityWorkers.length > 0) {
      recommendations.push(`Improve system prompts for: ${lowQualityWorkers.map(w => w.workerName).join(', ')}`);
    }

    return recommendations;
  }

  exportMetrics(): any {
    return {
      systemMetrics: this.getSystemMetrics(),
      workerMetrics: this.getAllWorkerMetrics(),
      taskMetrics: this.taskMetrics.slice(-100), // Last 100 tasks
      exportTime: new Date()
    };
  }
}
