"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommunicationProtocol = void 0;
const uuid_1 = require("uuid");
class CommunicationProtocol {
    constructor(logger) {
        this.messageQueue = new Map();
        this.activeCollaborations = new Map();
        this.knowledgeBase = [];
        this.messageHandlers = new Map();
        // Protocol configuration
        this.MESSAGE_TIMEOUT = 30000; // 30 seconds
        this.MAX_QUEUE_SIZE = 100;
        this.KNOWLEDGE_RETENTION_HOURS = 24;
        this.logger = logger;
        this.initializeProtocol();
    }
    initializeProtocol() {
        // Setup message processing
        setInterval(() => {
            this.processMessageQueues();
        }, 1000); // Process every second
        // Cleanup old knowledge
        setInterval(() => {
            this.cleanupKnowledgeBase();
        }, 300000); // Every 5 minutes
        this.logger.info('CommunicationProtocol', 'Communication protocol initialized');
    }
    registerMessageHandler(workerId, handler) {
        this.messageHandlers.set(workerId, handler);
        this.messageQueue.set(workerId, []);
        this.logger.debug('CommunicationProtocol', `Message handler registered for ${workerId}`);
    }
    sendMessage(message) {
        const fullMessage = {
            ...message,
            id: (0, uuid_1.v4)(),
            timestamp: new Date()
        };
        // Handle broadcast messages
        const recipients = Array.isArray(message.to) ? message.to : [message.to];
        recipients.forEach(recipient => {
            const queue = this.messageQueue.get(recipient);
            if (queue) {
                // Check queue size limit
                if (queue.length >= this.MAX_QUEUE_SIZE) {
                    queue.shift(); // Remove oldest message
                    this.logger.warn('CommunicationProtocol', `Message queue full for ${recipient}, dropping oldest message`);
                }
                queue.push(fullMessage);
                this.logger.debug('CommunicationProtocol', `Message queued for ${recipient}: ${message.type}`);
            }
            else {
                this.logger.warn('CommunicationProtocol', `No queue found for recipient: ${recipient}`);
            }
        });
        return fullMessage.id;
    }
    async processMessageQueues() {
        for (const [workerId, queue] of this.messageQueue) {
            if (queue.length === 0)
                continue;
            const handler = this.messageHandlers.get(workerId);
            if (!handler)
                continue;
            // Process messages by priority
            queue.sort((a, b) => this.getPriorityWeight(b.priority) - this.getPriorityWeight(a.priority));
            const messagesToProcess = queue.splice(0, 3); // Process up to 3 messages at once
            for (const message of messagesToProcess) {
                try {
                    await this.deliverMessage(workerId, message, handler);
                }
                catch (error) {
                    this.logger.error('CommunicationProtocol', `Error delivering message to ${workerId}: ${error}`);
                }
            }
        }
    }
    async deliverMessage(workerId, message, handler) {
        const startTime = Date.now();
        try {
            await handler(message);
            const deliveryTime = Date.now() - startTime;
            this.logger.debug('CommunicationProtocol', `Message delivered to ${workerId} in ${deliveryTime}ms: ${message.type}`);
        }
        catch (error) {
            this.logger.error('CommunicationProtocol', `Message delivery failed for ${workerId}: ${error}`);
            // Retry logic for important messages
            if (message.priority === 'urgent' || message.priority === 'high') {
                this.retryMessage(workerId, message);
            }
        }
    }
    retryMessage(workerId, message) {
        const retryCount = (message.metadata?.retryCount || 0) + 1;
        if (retryCount <= 3) {
            message.metadata = { ...message.metadata, retryCount };
            setTimeout(() => {
                const queue = this.messageQueue.get(workerId);
                if (queue) {
                    queue.unshift(message); // Add to front of queue
                }
            }, 5000 * retryCount); // Exponential backoff
            this.logger.info('CommunicationProtocol', `Retrying message delivery to ${workerId} (attempt ${retryCount})`);
        }
    }
    getPriorityWeight(priority) {
        switch (priority) {
            case 'urgent': return 4;
            case 'high': return 3;
            case 'normal': return 2;
            case 'low': return 1;
            default: return 2;
        }
    }
    initiateCollaboration(request) {
        const collaborationRequest = {
            ...request,
            requestId: (0, uuid_1.v4)()
        };
        this.activeCollaborations.set(collaborationRequest.requestId, collaborationRequest);
        // Notify all collaborators
        const message = {
            type: 'collaboration_request',
            from: request.initiator,
            to: request.collaborators,
            content: collaborationRequest,
            priority: 'high',
            requiresResponse: true,
            correlationId: collaborationRequest.requestId
        };
        this.sendMessage(message);
        this.logger.info('CommunicationProtocol', `Collaboration initiated: ${collaborationRequest.requestId} with ${request.collaborators.length} workers`);
        return collaborationRequest.requestId;
    }
    respondToCollaboration(requestId, workerId, accepted, capabilities) {
        const collaboration = this.activeCollaborations.get(requestId);
        if (!collaboration) {
            this.logger.warn('CommunicationProtocol', `Collaboration not found: ${requestId}`);
            return;
        }
        const response = {
            requestId,
            workerId,
            accepted,
            capabilities: capabilities || [],
            timestamp: new Date()
        };
        // Send response to initiator
        const message = {
            type: 'coordination',
            from: workerId,
            to: collaboration.initiator,
            content: { type: 'collaboration_response', data: response },
            priority: 'high',
            requiresResponse: false,
            correlationId: requestId
        };
        this.sendMessage(message);
        this.logger.debug('CommunicationProtocol', `Collaboration response sent: ${workerId} ${accepted ? 'accepted' : 'declined'} ${requestId}`);
    }
    shareKnowledge(share) {
        const knowledgeShare = {
            ...share,
            shareId: (0, uuid_1.v4)()
        };
        this.knowledgeBase.push(knowledgeShare);
        // Notify relevant workers
        if (share.relevantWorkers.length > 0) {
            const message = {
                type: 'knowledge_share',
                from: share.source,
                to: share.relevantWorkers,
                content: knowledgeShare,
                priority: 'normal',
                requiresResponse: false
            };
            this.sendMessage(message);
        }
        this.logger.info('CommunicationProtocol', `Knowledge shared: ${share.knowledgeType} from ${share.source} to ${share.relevantWorkers.length} workers`);
        return knowledgeShare.shareId;
    }
    queryKnowledge(workerId, query, knowledgeTypes) {
        const relevantKnowledge = this.knowledgeBase.filter(knowledge => {
            // Filter by knowledge type if specified
            if (knowledgeTypes && !knowledgeTypes.includes(knowledge.knowledgeType)) {
                return false;
            }
            // Check if worker is in relevant workers list
            if (!knowledge.relevantWorkers.includes(workerId) && !knowledge.relevantWorkers.includes('*')) {
                return false;
            }
            // Simple text matching (could be enhanced with NLP)
            const queryLower = query.toLowerCase();
            const knowledgeText = JSON.stringify(knowledge.knowledge).toLowerCase();
            return knowledgeText.includes(queryLower);
        });
        // Sort by confidence and recency
        relevantKnowledge.sort((a, b) => {
            const confidenceDiff = b.confidence - a.confidence;
            if (Math.abs(confidenceDiff) > 0.1)
                return confidenceDiff;
            // If confidence is similar, prefer more recent knowledge
            return new Date(b.shareId).getTime() - new Date(a.shareId).getTime();
        });
        this.logger.debug('CommunicationProtocol', `Knowledge query from ${workerId}: found ${relevantKnowledge.length} relevant items`);
        return relevantKnowledge.slice(0, 5); // Return top 5 results
    }
    coordinateTaskExecution(tasks, strategy) {
        const coordinationId = (0, uuid_1.v4)();
        const coordinationPlan = this.createCoordinationPlan(tasks, strategy);
        // Send coordination messages to involved workers
        coordinationPlan.phases.forEach((phase, index) => {
            phase.tasks.forEach((task) => {
                const message = {
                    type: 'coordination',
                    from: 'master',
                    to: task.assignedTo,
                    content: {
                        type: 'task_coordination',
                        coordinationId,
                        phase: index,
                        task,
                        dependencies: phase.dependencies,
                        strategy
                    },
                    priority: 'high',
                    requiresResponse: true,
                    correlationId: coordinationId
                };
                this.sendMessage(message);
            });
        });
        this.logger.info('CommunicationProtocol', `Task coordination initiated: ${coordinationId} with ${tasks.length} tasks using ${strategy} strategy`);
        return coordinationId;
    }
    createCoordinationPlan(tasks, strategy) {
        const plan = {
            coordinationId: (0, uuid_1.v4)(),
            strategy,
            phases: []
        };
        switch (strategy) {
            case 'parallel':
                // All tasks can run in parallel
                plan.phases.push({
                    tasks,
                    dependencies: []
                });
                break;
            case 'sequential':
                // Tasks run one after another
                tasks.forEach((task, index) => {
                    plan.phases.push({
                        tasks: [task],
                        dependencies: index > 0 ? [tasks[index - 1].id] : []
                    });
                });
                break;
            case 'hybrid':
                // Group related tasks that can run in parallel
                const groups = this.groupRelatedTasks(tasks);
                groups.forEach((group, index) => {
                    plan.phases.push({
                        tasks: group,
                        dependencies: index > 0 ? groups[index - 1].map(t => t.id) : []
                    });
                });
                break;
        }
        return plan;
    }
    groupRelatedTasks(tasks) {
        // Simple grouping by task type/complexity
        // In a real implementation, this could use more sophisticated analysis
        const groups = [];
        let currentGroup = [];
        tasks.forEach(task => {
            if (currentGroup.length === 0 || this.areTasksRelated(currentGroup[0], task)) {
                currentGroup.push(task);
            }
            else {
                groups.push(currentGroup);
                currentGroup = [task];
            }
        });
        if (currentGroup.length > 0) {
            groups.push(currentGroup);
        }
        return groups;
    }
    areTasksRelated(task1, task2) {
        // Simple relatedness check - could be enhanced
        const desc1 = task1.description.toLowerCase();
        const desc2 = task2.description.toLowerCase();
        const commonWords = desc1.split(' ').filter(word => desc2.includes(word) && word.length > 3);
        return commonWords.length > 0;
    }
    cleanupKnowledgeBase() {
        const cutoffTime = Date.now() - (this.KNOWLEDGE_RETENTION_HOURS * 60 * 60 * 1000);
        const initialCount = this.knowledgeBase.length;
        this.knowledgeBase = this.knowledgeBase.filter(knowledge => {
            // Keep high-confidence knowledge longer
            const retentionMultiplier = knowledge.confidence > 0.8 ? 2 : 1;
            const shareTime = new Date(knowledge.shareId).getTime();
            return (Date.now() - shareTime) < (cutoffTime * retentionMultiplier);
        });
        const removedCount = initialCount - this.knowledgeBase.length;
        if (removedCount > 0) {
            this.logger.debug('CommunicationProtocol', `Cleaned up ${removedCount} old knowledge entries`);
        }
    }
    getProtocolStatistics() {
        const totalMessages = Array.from(this.messageQueue.values())
            .reduce((sum, queue) => sum + queue.length, 0);
        return {
            activeQueues: this.messageQueue.size,
            totalQueuedMessages: totalMessages,
            activeCollaborations: this.activeCollaborations.size,
            knowledgeBaseSize: this.knowledgeBase.length,
            registeredHandlers: this.messageHandlers.size
        };
    }
    exportCommunicationData() {
        return {
            messageQueues: Object.fromEntries(this.messageQueue),
            activeCollaborations: Object.fromEntries(this.activeCollaborations),
            knowledgeBase: this.knowledgeBase.slice(-50), // Last 50 knowledge items
            statistics: this.getProtocolStatistics(),
            exportTime: new Date()
        };
    }
}
exports.CommunicationProtocol = CommunicationProtocol;
//# sourceMappingURL=communication-protocol.js.map