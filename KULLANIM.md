# Terminal AI Orchestrator (TAO) - Kullan<PERSON>m Kılavuzu

## 🚀 Kurulum

1. **Bağ<PERSON>mlılıkları yükleyin:**
```bash
npm install
```

2. **<PERSON><PERSON><PERSON> derleyin:**
```bash
npm run build
```

3. **Konfigürasyonu ayarlayın:**
`config/models.json` dosyasında API anahtarlarınızı güncelleyin:

```json
{
  "master": {
    "provider": {
      "baseURL": "https://api.openai.com/v1",
      "apiKey": "your-openai-api-key",
      "model": "gpt-4"
    }
  },
  "workers": [
    {
      "provider": {
        "apiKey": "your-openai-api-key"
      }
    }
  ]
}
```

## 🎯 Çalıştırma

### Temel Kullanım
```bash
npm start
# veya
node dist/index.js start
```

### Debug Modu
```bash
node dist/index.js start --debug
```

### Konfigüras<PERSON>ö<PERSON>ü<PERSON>üleme
```bash
node dist/index.js config
```

## 🤖 Sistem Mimarisi

### Master AI
- **Rol:** Ana orkestratör
- **Görevler:** 
  - Kullanıcı isteklerini analiz eder
  - Görevleri worker AI'lara dağıtır
  - Sonuçları koordine eder
  - Genel sohbeti yönetir

### Worker AI'lar

#### 1. Code Specialist (worker_1)
- **Uzmanlık:** Kod analizi
- **Yetenekler:**
  - Kod analizi
  - Debugging
  - Optimizasyon
  - Kod üretimi

#### 2. Research Assistant (worker_2)
- **Uzmanlık:** Araştırma
- **Yetenekler:**
  - Bilgi toplama
  - Analiz
  - Dokümantasyon
  - Doğruluk kontrolü

## 💬 Kullanım Örnekleri

### Kod Analizi İsteği
```
Kullanıcı: "Bu JavaScript kodunda hata var mı?"
↓
Master AI: Kodu analiz ediyor...
↓
Code Specialist: Detaylı kod analizi yapıyor
↓
Sonuç: Hata raporu ve öneriler
```

### Araştırma İsteği
```
Kullanıcı: "Node.js'in son sürümü nedir?"
↓
Master AI: Araştırma gerekiyor...
↓
Research Assistant: Güncel bilgileri araştırıyor
↓
Sonuç: Detaylı bilgi raporu
```

## 🎮 Terminal Arayüzü

### Paneller
- **Sol Panel:** Worker AI durumları
- **Orta Panel:** Sohbet geçmişi
- **Sağ Panel:** Sistem logları
- **Alt Panel:** Mesaj giriş alanı

### Klavye Kısayolları
- `F1` - Yardım
- `F2` - Worker durumlarını güncelle
- `F3` - Debug loglarını aç/kapat
- `ESC` - Çıkış

## ⚙️ Konfigürasyon

### Model Ayarları
```json
{
  "name": "Model Adı",
  "role": "master|worker",
  "specialization": "uzmanlık_alanı",
  "provider": {
    "baseURL": "API endpoint",
    "apiKey": "API anahtarı",
    "model": "model_adı"
  },
  "systemPrompt": "Sistem promptu",
  "capabilities": ["yetenek1", "yetenek2"],
  "maxTokens": 4000,
  "temperature": 0.7
}
```

### İletişim Ayarları
```json
{
  "communication": {
    "enableInterWorkerCommunication": true,
    "messageTimeout": 30000,
    "maxRetries": 3
  }
}
```

## 🔧 Özelleştirme

### Yeni Worker AI Ekleme
1. `config/models.json` dosyasına yeni worker ekleyin
2. Uzmanlık alanını ve yetenekleri tanımlayın
3. Sistem promptunu özelleştirin

### Yeni Provider Ekleme
Herhangi bir OpenAI uyumlu provider kullanabilirsiniz:
- OpenAI
- Anthropic Claude (API uyumlu)
- Local LLM'ler (Ollama, LM Studio)
- Azure OpenAI

## 🐛 Sorun Giderme

### Yaygın Hatalar

1. **API Anahtarı Hatası**
   - `config/models.json` dosyasında API anahtarlarını kontrol edin

2. **Bağlantı Hatası**
   - İnternet bağlantınızı kontrol edin
   - Provider URL'lerini doğrulayın

3. **Model Bulunamadı**
   - Model adlarının doğru olduğundan emin olun

### Debug Modu
```bash
node dist/index.js start --debug
```
Debug modunda detaylı loglar görüntülenir.

## 📝 Geliştirme

### Geliştirme Modu
```bash
npm run dev
```

### Dosya Yapısı
```
src/
├── index.ts          # Ana uygulama
├── master-ai.ts      # Master AI sınıfı
├── worker-ai.ts      # Worker AI sınıfı
├── ai-service.ts     # AI API servisi
├── terminal-ui.ts    # Terminal arayüzü
├── logger.ts         # Loglama sistemi
└── types.ts          # TypeScript tipleri
```

## 🚀 Gelecek Özellikler

- [ ] Web arayüzü
- [ ] Plugin sistemi
- [ ] Daha fazla AI provider desteği
- [ ] Görev geçmişi kaydetme
- [ ] Otomatik görev dağıtımı
- [ ] Multi-language desteği

## 📄 Lisans

MIT License - Detaylar için LICENSE dosyasına bakın.