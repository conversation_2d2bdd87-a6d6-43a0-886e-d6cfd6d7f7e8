import { LogEntry } from './types';
import chalk from 'chalk';

export class Logger {
  private logs: LogEntry[] = [];
  private maxLogs: number = 1000;
  private debugMode: boolean = false;

  constructor(debugMode: boolean = false) {
    this.debugMode = debugMode;
  }

  info(source: string, message: string, data?: any): void {
    this.log('info', source, message, data);
  }

  warn(source: string, message: string, data?: any): void {
    this.log('warn', source, message, data);
  }

  error(source: string, message: string, data?: any): void {
    this.log('error', source, message, data);
  }

  debug(source: string, message: string, data?: any): void {
    if (this.debugMode) {
      this.log('debug', source, message, data);
    }
  }

  private log(level: LogEntry['level'], source: string, message: string, data?: any): void {
    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      source,
      message,
      data
    };

    this.logs.push(entry);
    
    // Maksimum log sayısını aş
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Console'a yazdır
    this.printToConsole(entry);
  }

  private printToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toLocaleTimeString();
    const source = `[${entry.source}]`;
    
    let coloredLevel: string;
    switch (entry.level) {
      case 'info':
        coloredLevel = chalk.blue('INFO');
        break;
      case 'warn':
        coloredLevel = chalk.yellow('WARN');
        break;
      case 'error':
        coloredLevel = chalk.red('ERROR');
        break;
      case 'debug':
        coloredLevel = chalk.gray('DEBUG');
        break;
    }

    const logLine = `${chalk.gray(timestamp)} ${coloredLevel} ${chalk.cyan(source)} ${entry.message}`;
    console.log(logLine);
    
    if (entry.data && this.debugMode) {
      console.log(chalk.gray('  Data:'), entry.data);
    }
  }

  getLogs(level?: LogEntry['level'], source?: string, limit?: number): LogEntry[] {
    let filtered = this.logs;
    
    if (level) {
      filtered = filtered.filter(log => log.level === level);
    }
    
    if (source) {
      filtered = filtered.filter(log => log.source === source);
    }
    
    if (limit) {
      filtered = filtered.slice(-limit);
    }
    
    return filtered;
  }

  clear(): void {
    this.logs = [];
  }

  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
  }
}