"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MasterAI = void 0;
const uuid_1 = require("uuid");
const ai_service_1 = require("./ai-service");
const worker_ai_1 = require("./worker-ai");
const task_orchestrator_1 = require("./task-orchestrator");
const context_manager_1 = require("./context-manager");
const performance_monitor_1 = require("./performance-monitor");
const communication_protocol_1 = require("./communication-protocol");
class MasterAI {
    constructor(model, workers, logger) {
        this.workers = new Map();
        this.activeTasks = new Map();
        this.aiService = new ai_service_1.AIService(model);
        this.logger = logger;
        // Initialize advanced systems
        this.taskOrchestrator = new task_orchestrator_1.TaskOrchestrator(logger);
        this.contextManager = new context_manager_1.ContextManager(logger);
        this.performanceMonitor = new performance_monitor_1.PerformanceMonitor(logger);
        this.communicationProtocol = new communication_protocol_1.CommunicationProtocol(logger);
        this.context = {
            messages: [],
            participants: [model.name],
            sessionId: this.contextManager.getSessionStatistics().sessionId
        };
        // Worker AI'ları başlat
        workers.forEach(workerModel => {
            const worker = new worker_ai_1.WorkerAI(workerModel, this, logger);
            this.workers.set(workerModel.id, worker);
            this.context.participants.push(workerModel.name);
            // Register worker with advanced systems
            this.taskOrchestrator.registerWorker(worker);
            this.performanceMonitor.registerWorker(workerModel.id, workerModel.name, workerModel.specialization || 'general');
            this.communicationProtocol.registerMessageHandler(workerModel.id, this.handleWorkerMessage.bind(this));
        });
        this.logger.info('MasterAI', `Master AI başlatıldı. ${workers.length} worker AI aktif.`);
    }
    async processUserInput(input) {
        const userMessage = {
            id: (0, uuid_1.v4)(),
            from: 'user',
            to: 'master',
            content: input,
            timestamp: new Date(),
            type: 'user'
        };
        // Add to context manager
        this.contextManager.addMessage(userMessage);
        this.context.messages.push(userMessage);
        this.logger.debug('MasterAI', `Kullanıcı mesajı alındı: ${input.substring(0, 50)}...`);
        try {
            // Get enhanced context from context manager
            const contextWindow = this.contextManager.getContextWindow(input);
            const enhancedContext = this.contextManager.buildContextString();
            // Master AI'dan yanıt al
            const response = await this.aiService.sendMessage(contextWindow.recentMessages, enhancedContext);
            const masterResponse = {
                id: (0, uuid_1.v4)(),
                from: 'master',
                to: 'user',
                content: response.content,
                timestamp: new Date(),
                type: 'ai_response'
            };
            // Add to context manager
            this.contextManager.addMessage(masterResponse);
            this.context.messages.push(masterResponse);
            // Görev dağıtımı gerekli mi kontrol et
            await this.analyzeAndDistributeTasks(input, response.content);
            return response.content;
        }
        catch (error) {
            this.logger.error('MasterAI', `Hata: ${error}`);
            return `Üzgünüm, bir hata oluştu: ${error}`;
        }
    }
    async analyzeAndDistributeTasks(userInput, masterResponse) {
        try {
            // Use advanced task orchestrator for intelligent task distribution
            const task = await this.taskOrchestrator.distributeTask(userInput, this.contextManager.buildContextString());
            if (task) {
                this.activeTasks.set(task.id, task);
                // Start performance monitoring
                this.performanceMonitor.startTask(task.id, task.assignedTo, task.complexity || 0.5);
                // Find and assign to worker
                const worker = this.workers.get(task.assignedTo);
                if (worker) {
                    await worker.assignTask(task);
                    this.logger.info('MasterAI', `Advanced task distribution: ${task.description.substring(0, 50)}... assigned to ${worker.getName()}`);
                }
            }
            else {
                this.logger.warn('MasterAI', 'No suitable worker found for task distribution');
            }
        }
        catch (error) {
            this.logger.error('MasterAI', `Task distribution error: ${error}`);
        }
    }
    async createTask(description, assignedTo) {
        const task = {
            id: (0, uuid_1.v4)(),
            description,
            assignedTo,
            status: 'pending',
            createdAt: new Date()
        };
        this.activeTasks.set(task.id, task);
        this.logger.info('MasterAI', `Yeni görev oluşturuldu: ${task.id}`);
        return task;
    }
    async receiveWorkerResponse(workerId, taskId, result) {
        const task = this.activeTasks.get(taskId);
        if (task) {
            task.status = 'completed';
            task.result = result;
            task.completedAt = new Date();
            // Calculate response time and update performance
            const responseTime = task.completedAt.getTime() - task.createdAt.getTime();
            this.performanceMonitor.completeTask(taskId, true);
            // Update task orchestrator performance
            this.taskOrchestrator.updateWorkerPerformance(workerId, task, responseTime, true);
            this.logger.info('MasterAI', `Görev tamamlandı: ${taskId} by ${workerId}`);
            // Worker sonucunu context'e ekle
            const workerMessage = {
                id: (0, uuid_1.v4)(),
                from: workerId,
                to: 'master',
                content: `Görev Sonucu (${taskId}): ${result}`,
                timestamp: new Date(),
                type: 'inter_ai'
            };
            this.contextManager.addMessage(workerMessage);
            this.context.messages.push(workerMessage);
        }
    }
    async handleWorkerMessage(message) {
        try {
            this.logger.debug('MasterAI', `Received message from worker: ${message.type}`);
            switch (message.type) {
                case 'task_result':
                    await this.receiveWorkerResponse(message.from, message.content.taskId, message.content.result);
                    break;
                case 'collaboration_request':
                    await this.handleCollaborationRequest(message);
                    break;
                case 'knowledge_share':
                    await this.handleKnowledgeShare(message);
                    break;
                case 'status_update':
                    await this.handleStatusUpdate(message);
                    break;
                default:
                    this.logger.warn('MasterAI', `Unknown message type: ${message.type}`);
            }
        }
        catch (error) {
            this.logger.error('MasterAI', `Error handling worker message: ${error}`);
        }
    }
    async handleCollaborationRequest(message) {
        // Handle collaboration requests between workers
        this.logger.info('MasterAI', `Collaboration request from ${message.from}`);
    }
    async handleKnowledgeShare(message) {
        // Handle knowledge sharing between workers
        this.logger.info('MasterAI', `Knowledge shared by ${message.from}`);
    }
    async handleStatusUpdate(message) {
        // Handle status updates from workers
        this.logger.debug('MasterAI', `Status update from ${message.from}: ${message.content}`);
    }
    buildContextString() {
        const activeWorkers = Array.from(this.workers.values())
            .map(w => `${w.getName()} (${w.getSpecialization()})`)
            .join(', ');
        const activeTasks = Array.from(this.activeTasks.values())
            .filter(t => t.status !== 'completed')
            .map(t => `${t.id}: ${t.description} (${t.status})`)
            .join('\n');
        return `
Aktif Worker AI'lar: ${activeWorkers}
Aktif Görevler: 
${activeTasks || 'Yok'}
Son Mesajlar: ${this.context.messages.slice(-3).map(m => `${m.from}: ${m.content.substring(0, 100)}`).join('\n')}
    `.trim();
    }
    getWorkers() {
        return Array.from(this.workers.values());
    }
    getActiveTasks() {
        return Array.from(this.activeTasks.values());
    }
    getContext() {
        return this.context;
    }
    async broadcastToWorkers(message) {
        const promises = Array.from(this.workers.values()).map(worker => worker.receiveMessage(message, 'master'));
        await Promise.all(promises);
        this.logger.info('MasterAI', 'Mesaj tüm worker AI\'lara gönderildi');
    }
    // New advanced methods
    getPerformanceReport() {
        return this.performanceMonitor.getPerformanceReport();
    }
    getSystemStatistics() {
        return {
            performance: this.performanceMonitor.getSystemMetrics(),
            taskOrchestrator: this.taskOrchestrator.getAllStatistics(),
            context: this.contextManager.getSessionStatistics(),
            communication: this.communicationProtocol.getProtocolStatistics()
        };
    }
    updateUserPreference(key, value) {
        this.contextManager.updateUserPreference(key, value);
    }
    getUserPreference(key) {
        return this.contextManager.getUserPreference(key);
    }
    async initiateWorkerCollaboration(objective, workerIds) {
        const collaborationId = this.communicationProtocol.initiateCollaboration({
            initiator: 'master',
            collaborators: workerIds,
            objective,
            taskContext: this.contextManager.getContextWindow(),
            coordinationStrategy: 'hybrid'
        });
        this.logger.info('MasterAI', `Worker collaboration initiated: ${collaborationId}`);
        return collaborationId;
    }
    shareKnowledgeWithWorkers(knowledge, knowledgeType, relevantWorkers) {
        this.communicationProtocol.shareKnowledge({
            source: 'master',
            knowledge,
            relevantWorkers,
            knowledgeType: knowledgeType,
            confidence: 0.9
        });
    }
    resetContext() {
        this.contextManager.reset();
        this.context = {
            messages: [],
            participants: this.context.participants,
            sessionId: this.contextManager.getSessionStatistics().sessionId
        };
        this.logger.info('MasterAI', 'Context reset completed');
    }
}
exports.MasterAI = MasterAI;
//# sourceMappingURL=master-ai.js.map