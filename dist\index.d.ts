#!/usr/bin/env node
import { Logger } from './logger';
declare class TerminalAIOrchestrator {
    private config;
    private masterAI;
    logger: Logger;
    private ui;
    private isRunning;
    constructor();
    private loadConfiguration;
    private initializeSystem;
    start(): Promise<void>;
    private showSystemInfo;
    interactiveMode(): Promise<void>;
    private showHelp;
    private showStatus;
}
export { TerminalAIOrchestrator };
//# sourceMappingURL=index.d.ts.map