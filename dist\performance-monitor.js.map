{"version": 3, "file": "performance-monitor.js", "sourceRoot": "", "sources": ["../src/performance-monitor.ts"], "names": [], "mappings": ";;;AAwCA,MAAa,kBAAkB;IAY7B,YAAY,MAAc;QAXlB,kBAAa,GAAoC,IAAI,GAAG,EAAE,CAAC;QAC3D,gBAAW,GAAkB,EAAE,CAAC;QAIxC,yBAAyB;QACR,4BAAuB,GAAG,KAAK,CAAC,CAAC,aAAa;QAC9C,2BAAsB,GAAG,IAAI,CAAC,CAAC,MAAM;QACrC,yBAAoB,GAAG,CAAC,CAAC,CAAC,qBAAqB;QAC/C,sBAAiB,GAAG,GAAG,CAAC,CAAC,eAAe;QAGvD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;QAClC,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAEO,wBAAwB;QAC9B,gCAAgC;QAChC,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC5B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,eAAe;QAE1B,sBAAsB;QACtB,WAAW,CAAC,GAAG,EAAE;YACf,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,kBAAkB;QAE9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,8BAA8B,CAAC,CAAC;IACzE,CAAC;IAED,cAAc,CAAC,QAAgB,EAAE,UAAkB,EAAE,cAAsB;QACzE,MAAM,OAAO,GAAuB;YAClC,QAAQ;YACR,UAAU;YACV,UAAU,EAAE,CAAC;YACb,cAAc,EAAE,CAAC;YACjB,WAAW,EAAE,CAAC;YACd,mBAAmB,EAAE,CAAC;YACtB,WAAW,EAAE,GAAG;YAChB,UAAU,EAAE,CAAC;YACb,YAAY,EAAE,GAAG;YACjB,UAAU,EAAE,IAAI,IAAI,EAAE;YACtB,cAAc;SACf,CAAC;QAEF,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,sBAAsB,UAAU,EAAE,CAAC,CAAC;IAC7E,CAAC;IAED,SAAS,CAAC,MAAc,EAAE,QAAgB,EAAE,UAAkB;QAC5D,MAAM,UAAU,GAAgB;YAC9B,MAAM;YACN,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,OAAO,EAAE,KAAK;YACd,UAAU;YACV,UAAU,EAAE,CAAC;SACd,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAElC,wBAAwB;QACxB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACvD,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,UAAU,EAAE,CAAC;YAC3B,aAAa,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,iBAAiB,MAAM,eAAe,QAAQ,EAAE,CAAC,CAAC;IAC5F,CAAC;IAED,YAAY,CAAC,MAAc,EAAE,OAAgB,EAAE,YAAqB;QAClE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QACnE,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE,0BAA0B,MAAM,EAAE,CAAC,CAAC;YAC3E,OAAO;QACT,CAAC;QAED,UAAU,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAChC,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACxF,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;QAC7B,UAAU,CAAC,YAAY,GAAG,YAAY,CAAC;QAEvC,wBAAwB;QACxB,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAClE,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,OAAO,EAAE,CAAC;gBACZ,aAAa,CAAC,cAAc,EAAE,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,aAAa,CAAC,WAAW,EAAE,CAAC;YAC9B,CAAC;YAED,sBAAsB;YACtB,aAAa,CAAC,WAAW,GAAG,aAAa,CAAC,cAAc,GAAG,aAAa,CAAC,UAAU,CAAC;YAEpF,+BAA+B;YAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACjD,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,YAAY,CAClE,CAAC;YAEF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,aAAa,CAAC,mBAAmB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CACnE,GAAG,GAAG,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,cAAc,CAAC,MAAM,CAAC;YAC5D,CAAC;YAED,uCAAuC;YACvC,MAAM,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,WAAW;YACzC,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC9C,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ;gBAClC,CAAC,CAAC,OAAO;gBACT,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,GAAG,UAAU,CAChD,CAAC;YACF,aAAa,CAAC,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC;YAE9C,uBAAuB;YACvB,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACpD,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,CACrD,CAAC;YAEF,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACjC,aAAa,CAAC,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAC/D,GAAG,GAAG,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM,CAAC;YAC/D,CAAC;YAED,aAAa,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QACxC,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EACpC,mBAAmB,MAAM,cAAc,OAAO,WAAW,UAAU,CAAC,YAAY,IAAI,CAAC,CAAC;IAC1F,CAAC;IAED,SAAS,CAAC,MAAc;QACtB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QACnE,IAAI,UAAU,EAAE,CAAC;YACf,UAAU,CAAC,UAAU,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,eAAe,MAAM,YAAY,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC;QACpG,CAAC;IACH,CAAC;IAED,gBAAgB,CAAC,QAAgB;QAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC;IAED,mBAAmB;QACjB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,gBAAgB;QACd,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;QAClC,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC;QAElE,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;QAC3D,MAAM,WAAW,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAE9C,MAAM,iBAAiB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5F,MAAM,mBAAmB,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtG,MAAM,cAAc,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;QACjF,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjF,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;YAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAC5D,OAAO,eAAe,GAAG,MAAM,CAAC,CAAC,2BAA2B;QAC9D,CAAC,CAAC,CAAC,MAAM,CAAC;QAEV,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC;YACxD,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,iBAAiB,CAAC,MAAM;YACjG,CAAC,CAAC,CAAC,CAAC;QAEN,OAAO;YACL,aAAa,EAAE,QAAQ,CAAC,MAAM;YAC9B,yBAAyB,EAAE,mBAAmB;YAC9C,YAAY,EAAE,MAAM;YACpB,aAAa;YACb,cAAc;YACd,SAAS;YACT,mBAAmB,EAAE,IAAI,CAAC,4BAA4B,EAAE;YACxD,qBAAqB;SACtB,CAAC;IACJ,CAAC;IAEO,4BAA4B;QAClC,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEtC,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;YACvD,6DAA6D;YAC7D,MAAM,aAAa,GAAG,EAAE,CAAC,CAAC,iCAAiC;YAC3D,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,aAAa,EAAE,CAAC,CAAC,CAAC;YAC5E,MAAM,WAAW,GAAG,oBAAoB,GAAG,MAAM,CAAC,WAAW,CAAC;YAC9D,OAAO,GAAG,GAAG,WAAW,CAAC;QAC3B,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,OAAO,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC;IAC5C,CAAC;IAEO,kBAAkB;QACxB,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEjD,wBAAwB;QACxB,IAAI,aAAa,CAAC,yBAAyB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC3E,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EACnC,8BAA8B,aAAa,CAAC,yBAAyB,IAAI,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,aAAa,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC;YAChE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EACnC,2BAA2B,CAAC,aAAa,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9E,CAAC;QAED,wBAAwB;QACxB,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YAC7B,MAAM,MAAM,GAAa,EAAE,CAAC;YAE5B,IAAI,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC9D,MAAM,CAAC,IAAI,CAAC,kBAAkB,MAAM,CAAC,mBAAmB,KAAK,CAAC,CAAC;YACjE,CAAC;YAED,IAAI,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;gBACrD,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC9E,CAAC;YAED,IAAI,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAClD,MAAM,CAAC,IAAI,CAAC,mBAAmB,MAAM,CAAC,UAAU,aAAa,CAAC,CAAC;YACjE,CAAC;YAED,IAAI,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACjD,MAAM,CAAC,IAAI,CAAC,sBAAsB,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YACzE,CAAC;YAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EACnC,UAAU,MAAM,CAAC,UAAU,wBAAwB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAC5E,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EACnC,yCAAyC,aAAa,CAAC,aAAa,IAAI;YACxE,iBAAiB,aAAa,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;YACzE,eAAe,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;YACrE,eAAe,CAAC,aAAa,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAClE,CAAC;IAEO,iBAAiB;QACvB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,eAAe;QAEtE,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC7C,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,UAAU,CACnC,CAAC;QAEF,MAAM,YAAY,GAAG,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QAC5D,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,cAAc,YAAY,mBAAmB,CAAC,CAAC;QACzF,CAAC;IACH,CAAC;IAED,oBAAoB;QAClB,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAEjD,+BAA+B;QAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC7D,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChD,GAAG,MAAM;YACT,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;SACzC,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE;gBACb,GAAG,aAAa;gBAChB,KAAK,EAAE,WAAW;aACnB;YACD,aAAa,EAAE,YAAY;YAC3B,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,aAAa,EAAE,aAAa,CAAC;SAC5E,CAAC;IACJ,CAAC;IAEO,oBAAoB,CAAC,OAAsB;QACjD,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,IAAI,OAAO,CAAC,yBAAyB,GAAG,IAAI,CAAC,uBAAuB;YAAE,KAAK,IAAI,EAAE,CAAC;QAClF,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI;YAAE,KAAK,IAAI,EAAE,CAAC;QAC1C,IAAI,OAAO,CAAC,mBAAmB,GAAG,GAAG;YAAE,KAAK,IAAI,EAAE,CAAC;QACnD,IAAI,OAAO,CAAC,qBAAqB,GAAG,GAAG;YAAE,KAAK,IAAI,EAAE,CAAC;QACrD,IAAI,OAAO,CAAC,cAAc,GAAG,CAAC;YAAE,KAAK,IAAI,EAAE,CAAC;QAE5C,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,oBAAoB,CAAC,MAA0B;QACrD,IAAI,KAAK,GAAG,GAAG,CAAC;QAEhB,IAAI,MAAM,CAAC,mBAAmB,GAAG,IAAI,CAAC,uBAAuB;YAAE,KAAK,IAAI,EAAE,CAAC;QAC3E,IAAI,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB;YAAE,KAAK,IAAI,EAAE,CAAC;QAClE,IAAI,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB;YAAE,KAAK,IAAI,EAAE,CAAC;QAC/D,IAAI,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB;YAAE,KAAK,IAAI,EAAE,CAAC;QAE9D,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,GAAG,CAAC;QAC5B,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,uBAAuB,CAAC,aAA4B,EAAE,aAAmC;QAC/F,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,yBAAyB;QACzB,IAAI,aAAa,CAAC,yBAAyB,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC3E,eAAe,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,aAAa,CAAC,SAAS,GAAG,IAAI,EAAE,CAAC;YACnC,eAAe,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAC7E,CAAC;QAED,IAAI,aAAa,CAAC,mBAAmB,GAAG,GAAG,EAAE,CAAC;YAC5C,eAAe,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC;QAC3G,CAAC;QAED,yBAAyB;QACzB,MAAM,sBAAsB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CACtD,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB;YAC3C,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,uBAAuB,CACrD,CAAC;QAEF,IAAI,sBAAsB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,eAAe,CAAC,IAAI,CAAC,qCAAqC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxH,CAAC;QAED,MAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC7F,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,eAAe,CAAC,IAAI,CAAC,+BAA+B,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC7G,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,aAAa;QACX,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;YACtC,aAAa,EAAE,IAAI,CAAC,mBAAmB,EAAE;YACzC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,iBAAiB;YAC5D,UAAU,EAAE,IAAI,IAAI,EAAE;SACvB,CAAC;IACJ,CAAC;CACF;AAvWD,gDAuWC"}