import { Message } from './types';
import { Logger } from './logger';
export interface ContextSummary {
    id: string;
    summary: string;
    keyTopics: string[];
    participants: string[];
    timeRange: {
        start: Date;
        end: Date;
    };
    messageCount: number;
    importance: number;
}
export interface ContextWindow {
    recentMessages: Message[];
    relevantSummaries: ContextSummary[];
    activeTopics: string[];
    userPreferences: Map<string, any>;
    sessionMetadata: any;
}
export declare class ContextManager {
    private conversationHistory;
    private contextSummaries;
    private activeTopics;
    private userPreferences;
    private sessionMetadata;
    private logger;
    private readonly MAX_RECENT_MESSAGES;
    private readonly MAX_CONTEXT_SUMMARIES;
    private readonly SUMMARY_THRESHOLD;
    private readonly RELEVANCE_THRESHOLD;
    constructor(logger: Logger);
    private initializeSession;
    private generateSessionId;
    addMessage(message: Message): void;
    private extractAndUpdateTopics;
    private pruneInactiveTopics;
    private createContextSummary;
    private generateSummary;
    private calculateImportance;
    getContextWindow(currentMessage?: string): ContextWindow;
    private findRelevantSummaries;
    private calculateRelevanceScore;
    private extractTopicsFromText;
    updateUserPreference(key: string, value: any): void;
    getUserPreference(key: string): any;
    getSessionStatistics(): any;
    buildContextString(includeHistory?: boolean): string;
    reset(): void;
}
//# sourceMappingURL=context-manager.d.ts.map