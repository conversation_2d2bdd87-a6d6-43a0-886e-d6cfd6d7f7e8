{"name": "@types/blessed", "version": "0.1.25", "description": "TypeScript definitions for blessed", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/blessed", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "brynbell<PERSON>", "url": "https://github.com/brynbellomy"}, {"name": "<PERSON>", "githubUsername": "skellock", "url": "https://github.com/skellock"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mama<PERSON>ko"}, {"name": "<PERSON>", "githubUsername": "TooTallNate", "url": "https://github.com/TooTallNate"}, {"name": "<PERSON>", "githubUsername": "danikaze", "url": "https://github.com/danikaze"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jeffhuys"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/blessed"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "0d759675e2863c5adbbc1bf3586da2f177132362dc1df83baca0e6ae8d2dcb61", "typeScriptVersion": "4.5"}