{"width": 8, "height": 14, "glyphs": {"0": {"ch": "0", "code": 48, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--  --- ", "-- ---- ", "---- -- ", "---  -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "1": {"ch": "1", "code": 49, "map": ["        ", "        ", "   --   ", "  ---   ", " ----   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", " ------ ", "        ", "        "]}, "2": {"ch": "2", "code": 50, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "     -- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "------- ", "        ", "        "]}, "3": {"ch": "3", "code": 51, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "     -- ", "  ----  ", "     -- ", "     -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "4": {"ch": "4", "code": 52, "map": ["        ", "        ", "     -- ", "    --- ", "   ---- ", "  -- -- ", " --  -- ", "--   -- ", "------- ", "     -- ", "     -- ", "     -- ", "        ", "        "]}, "5": {"ch": "5", "code": 53, "map": ["        ", "        ", "------- ", "--      ", "--      ", "--      ", "------  ", "     -- ", "     -- ", "     -- ", "--   -- ", " -----  ", "        ", "        "]}, "6": {"ch": "6", "code": 54, "map": ["        ", "        ", "  ----  ", " --     ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "7": {"ch": "7", "code": 55, "map": ["        ", "        ", "------- ", "     -- ", "     -- ", "    --  ", "    --  ", "   --   ", "   --   ", "  --    ", "  --    ", "  --    ", "        ", "        "]}, "8": {"ch": "8", "code": 56, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "9": {"ch": "9", "code": 57, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "    --  ", " ----   ", "        ", "        "]}, "\u0000": {"ch": "\u0000", "code": 0, "map": ["        ", "        ", "--- --- ", "--   -- ", "        ", "--   -- ", "--   -- ", "--   -- ", "        ", "--   -- ", "--   -- ", "--- --- ", "        ", "        "]}, " ": {"ch": " ", "code": 32, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "!": {"ch": "!", "code": 33, "map": ["        ", "        ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "   --   ", "   --   ", "        ", "        "]}, "\"": {"ch": "\"", "code": 34, "map": ["        ", " --  -- ", " --  -- ", " --  -- ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "#": {"ch": "#", "code": 35, "map": ["        ", "        ", " -- --  ", " -- --  ", " -- --  ", "------- ", " -- --  ", " -- --  ", "------- ", " -- --  ", " -- --  ", " -- --  ", "        ", "        "]}, "$": {"ch": "$", "code": 36, "map": ["        ", "   -    ", "   -    ", " -----  ", "-- - -- ", "-- -    ", "-- -    ", " -----  ", "   - -- ", "   - -- ", "-- - -- ", " -----  ", "   -    ", "   -    "]}, "%": {"ch": "%", "code": 37, "map": ["        ", "        ", " --  -- ", "-- - -- ", " -- --  ", "    --  ", "   --   ", "   --   ", "  --    ", "  -- -- ", " -- - --", " --  -- ", "        ", "        "]}, "&": {"ch": "&", "code": 38, "map": ["        ", "        ", "  ---   ", " -- --  ", " -- --  ", "  ---   ", " --- -- ", "-- ---  ", "--  --  ", "--  --  ", "-- ---  ", " --- -- ", "        ", "        "]}, "'": {"ch": "'", "code": 39, "map": ["        ", "   --   ", "   --   ", "   --   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "(": {"ch": "(", "code": 40, "map": ["        ", "        ", "    --  ", "   --   ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "   --   ", "    --  ", "        ", "        "]}, ")": {"ch": ")", "code": 41, "map": ["        ", "        ", "  --    ", "   --   ", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "   --   ", "  --    ", "        ", "        "]}, "*": {"ch": "*", "code": 42, "map": ["        ", "        ", "        ", "        ", "        ", " -- --  ", "  ---   ", "------- ", "  ---   ", " -- --  ", "        ", "        ", "        ", "        "]}, "+": {"ch": "+", "code": 43, "map": ["        ", "        ", "        ", "        ", "        ", "   --   ", "   --   ", " ------ ", "   --   ", "   --   ", "        ", "        ", "        ", "        "]}, ",": {"ch": ",", "code": 44, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "   --   ", "   --   ", "  --    ", "        "]}, "-": {"ch": "-", "code": 45, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "------- ", "        ", "        ", "        ", "        ", "        ", "        "]}, ".": {"ch": ".", "code": 46, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "   --   ", "   --   ", "        ", "        "]}, "/": {"ch": "/", "code": 47, "map": ["        ", "        ", "     -- ", "     -- ", "    --  ", "    --  ", "   --   ", "   --   ", "  --    ", "  --    ", " --     ", " --     ", "        ", "        "]}, ":": {"ch": ":", "code": 58, "map": ["        ", "        ", "        ", "        ", "        ", "   --   ", "   --   ", "        ", "        ", "        ", "   --   ", "   --   ", "        ", "        "]}, ";": {"ch": ";", "code": 59, "map": ["        ", "        ", "        ", "        ", "        ", "   --   ", "   --   ", "        ", "        ", "        ", "   --   ", "   --   ", "  --    ", "        "]}, "<": {"ch": "<", "code": 60, "map": ["        ", "        ", "        ", "     -- ", "    --  ", "   --   ", "  --    ", " --     ", "  --    ", "   --   ", "    --  ", "     -- ", "        ", "        "]}, "=": {"ch": "=", "code": 61, "map": ["        ", "        ", "        ", "        ", "        ", "------- ", "        ", "        ", "------- ", "        ", "        ", "        ", "        ", "        "]}, ">": {"ch": ">", "code": 62, "map": ["        ", "        ", "        ", " --     ", "  --    ", "   --   ", "    --  ", "     -- ", "    --  ", "   --   ", "  --    ", " --     ", "        ", "        "]}, "?": {"ch": "?", "code": 63, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "    --  ", "   --   ", "   --   ", "        ", "   --   ", "   --   ", "        ", "        "]}, "@": {"ch": "@", "code": 64, "map": ["        ", "        ", " -----  ", "--   -- ", "--  --- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "--  --- ", "--      ", " ------ ", "        ", "        "]}, "A": {"ch": "A", "code": 65, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "B": {"ch": "B", "code": 66, "map": ["        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "        ", "        "]}, "C": {"ch": "C", "code": 67, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "--      ", "--      ", "--      ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "D": {"ch": "D", "code": 68, "map": ["        ", "        ", "-----   ", "--  --  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--  --  ", "-----   ", "        ", "        "]}, "E": {"ch": "E", "code": 69, "map": ["        ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "F": {"ch": "F", "code": 70, "map": ["        ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "G": {"ch": "G", "code": 71, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "--      ", "-- ---- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "H": {"ch": "H", "code": 72, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "I": {"ch": "I", "code": 73, "map": ["        ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "J": {"ch": "J", "code": 74, "map": ["        ", "        ", "   ---- ", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "--  --  ", "--  --  ", " ----   ", "        ", "        "]}, "K": {"ch": "K", "code": 75, "map": ["        ", "        ", "--   -- ", "--   -- ", "--  --  ", "-- --   ", "----    ", "----    ", "-- --   ", "--  --  ", "--   -- ", "--   -- ", "        ", "        "]}, "L": {"ch": "L", "code": 76, "map": ["        ", "        ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "M": {"ch": "M", "code": 77, "map": ["        ", "        ", "-     - ", "--   -- ", "--- --- ", "------- ", "-- - -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "N": {"ch": "N", "code": 78, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "---  -- ", "---- -- ", "-- ---- ", "--  --- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "O": {"ch": "O", "code": 79, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "P": {"ch": "P", "code": 80, "map": ["        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "Q": {"ch": "Q", "code": 81, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "-- ---- ", " -----  ", "     -- ", "        "]}, "R": {"ch": "R", "code": 82, "map": ["        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "----    ", "-- --   ", "--  --  ", "--   -- ", "        ", "        "]}, "S": {"ch": "S", "code": 83, "map": ["        ", "        ", " -----  ", "--   -- ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "T": {"ch": "T", "code": 84, "map": ["        ", "        ", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "U": {"ch": "U", "code": 85, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "V": {"ch": "V", "code": 86, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", " -- --  ", "  ---   ", "  ---   ", "        ", "        "]}, "W": {"ch": "W", "code": 87, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "-- - -- ", "------- ", "--- --- ", "--   -- ", "-     - ", "        ", "        "]}, "X": {"ch": "X", "code": 88, "map": ["        ", "        ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", "  ---   ", "  ---   ", " -- --  ", " -- --  ", "--   -- ", "--   -- ", "        ", "        "]}, "Y": {"ch": "Y", "code": 89, "map": ["        ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "Z": {"ch": "Z", "code": 90, "map": ["        ", "        ", "------- ", "     -- ", "     -- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "--      ", "------- ", "        ", "        "]}, "[": {"ch": "[", "code": 91, "map": ["        ", "        ", "  ----  ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "  ----  ", "        ", "        "]}, "\\": {"ch": "\\", "code": 92, "map": ["        ", "        ", " --     ", " --     ", "  --    ", "  --    ", "   --   ", "   --   ", "    --  ", "    --  ", "     -- ", "     -- ", "        ", "        "]}, "]": {"ch": "]", "code": 93, "map": ["        ", "        ", "  ----  ", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "  ----  ", "        ", "        "]}, "^": {"ch": "^", "code": 94, "map": ["        ", "   --   ", "  ----  ", " --  -- ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "_": {"ch": "_", "code": 95, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "------- ", "        "]}, "`": {"ch": "`", "code": 96, "map": ["  --    ", "   --   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "a": {"ch": "a", "code": 97, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "b": {"ch": "b", "code": 98, "map": ["        ", "        ", "--      ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "        ", "        "]}, "c": {"ch": "c", "code": 99, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", " -----  ", "        ", "        "]}, "d": {"ch": "d", "code": 100, "map": ["        ", "        ", "     -- ", "     -- ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "e": {"ch": "e", "code": 101, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "f": {"ch": "f", "code": 102, "map": ["        ", "        ", "   ---- ", "  --    ", "  --    ", "------  ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "        ", "        "]}, "g": {"ch": "g", "code": 103, "map": ["        ", "        ", "        ", "        ", "        ", " ------ ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "h": {"ch": "h", "code": 104, "map": ["        ", "        ", "--      ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "i": {"ch": "i", "code": 105, "map": ["        ", "        ", "   --   ", "   --   ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "j": {"ch": "j", "code": 106, "map": ["        ", "        ", "     -- ", "     -- ", "        ", "    --- ", "     -- ", "     -- ", "     -- ", "     -- ", "     -- ", " --  -- ", " --  -- ", "  ----  "]}, "k": {"ch": "k", "code": 107, "map": ["        ", "        ", "--      ", "--      ", "--      ", "--   -- ", "--  --  ", "-- --   ", "----    ", "-- --   ", "--  --  ", "--   -- ", "        ", "        "]}, "l": {"ch": "l", "code": 108, "map": ["        ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "m": {"ch": "m", "code": 109, "map": ["        ", "        ", "        ", "        ", "        ", "------  ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "        ", "        "]}, "n": {"ch": "n", "code": 110, "map": ["        ", "        ", "        ", "        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "o": {"ch": "o", "code": 111, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "p": {"ch": "p", "code": 112, "map": ["        ", "        ", "        ", "        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--      ", "--      "]}, "q": {"ch": "q", "code": 113, "map": ["        ", "        ", "        ", "        ", "        ", " ------ ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- "]}, "r": {"ch": "r", "code": 114, "map": ["        ", "        ", "        ", "        ", "        ", "-- ---- ", "----    ", "---     ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "s": {"ch": "s", "code": 115, "map": ["        ", "        ", "        ", "        ", "        ", " ------ ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "------  ", "        ", "        "]}, "t": {"ch": "t", "code": 116, "map": ["        ", "        ", "  --    ", "  --    ", "  --    ", "------  ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "   ---- ", "        ", "        "]}, "u": {"ch": "u", "code": 117, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "v": {"ch": "v", "code": 118, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", "  ---   ", "  ---   ", "        ", "        "]}, "w": {"ch": "w", "code": 119, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "        ", "        "]}, "x": {"ch": "x", "code": 120, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", " -- --  ", "  ---   ", " -- --  ", "--   -- ", "--   -- ", "        ", "        "]}, "y": {"ch": "y", "code": 121, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "z": {"ch": "z", "code": 122, "map": ["        ", "        ", "        ", "        ", "        ", "------- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "------- ", "        ", "        "]}, "{": {"ch": "{", "code": 123, "map": ["        ", "        ", "   ---  ", "  --    ", "  --    ", "  --    ", " --     ", "  --    ", "  --    ", "  --    ", "  --    ", "   ---  ", "        ", "        "]}, "|": {"ch": "|", "code": 124, "map": ["        ", "        ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "}": {"ch": "}", "code": 125, "map": ["        ", "        ", " ---    ", "   --   ", "   --   ", "   --   ", "    --  ", "   --   ", "   --   ", "   --   ", "   --   ", " ---    ", "        ", "        "]}, "~": {"ch": "~", "code": 126, "map": ["        ", " ---  --", "-- -- --", "--  --- ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, " ": {"ch": " ", "code": 160, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "¡": {"ch": "¡", "code": 161, "map": ["        ", "        ", "   --   ", "   --   ", "        ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "¢": {"ch": "¢", "code": 162, "map": ["        ", "        ", "        ", "   -    ", "   -    ", " -----  ", "-- - -- ", "-- -    ", "-- -    ", "-- -    ", "-- - -- ", " -----  ", "   -    ", "   -    "]}, "£": {"ch": "£", "code": 163, "map": ["        ", "        ", "  ---   ", " -- --  ", " --     ", " --     ", "-----   ", " --     ", " --     ", " --     ", " --  -- ", "------- ", "        ", "        "]}, "¤": {"ch": "¤", "code": 164, "map": ["        ", "        ", "        ", "        ", " --  -- ", "  ----  ", " --  -- ", " --  -- ", " --  -- ", "  ----  ", " --  -- ", "        ", "        ", "        "]}, "¥": {"ch": "¥", "code": 165, "map": ["        ", "        ", "--    --", "--    --", " --  -- ", "  ----  ", "   --   ", " ------ ", "   --   ", " ------ ", "   --   ", "   --   ", "        ", "        "]}, "¦": {"ch": "¦", "code": 166, "map": ["        ", "        ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "§": {"ch": "§", "code": 167, "map": ["        ", "  ----  ", " --  -- ", " --     ", "  ---   ", " -- --  ", " --  -- ", " --  -- ", "  -- -- ", "   ---  ", "     -- ", " --  -- ", "  ----  ", "        "]}, "¨": {"ch": "¨", "code": 168, "map": [" -- --  ", " -- --  ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "©": {"ch": "©", "code": 169, "map": ["        ", "        ", "        ", " ------ ", "-      -", "-  --  -", "- -  - -", "- -    -", "- -  - -", "-  --  -", "-      -", " ------ ", "        ", "        "]}, "ª": {"ch": "ª", "code": 170, "map": ["        ", "  ----  ", "     -- ", "  ----- ", " --  -- ", "  ----- ", "        ", " ------ ", "        ", "        ", "        ", "        ", "        ", "        "]}, "«": {"ch": "«", "code": 171, "map": ["        ", "        ", "        ", "        ", "        ", "   -- --", "  -- -- ", " -- --  ", "-- --   ", " -- --  ", "  -- -- ", "   -- --", "        ", "        "]}, "¬": {"ch": "¬", "code": 172, "map": ["        ", "        ", "        ", "        ", "        ", "------- ", "     -- ", "     -- ", "     -- ", "        ", "        ", "        ", "        ", "        "]}, "­": {"ch": "­", "code": 173, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", " -----  ", "        ", "        ", "        ", "        ", "        ", "        "]}, "®": {"ch": "®", "code": 174, "map": ["        ", "        ", "        ", " ------ ", "-      -", "- ---  -", "- -  - -", "- ---  -", "- - -  -", "- -  - -", "-      -", " ------ ", "        ", "        "]}, "¯": {"ch": "¯", "code": 175, "map": [" -----  ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "°": {"ch": "°", "code": 176, "map": ["        ", "  ---   ", " -- --  ", " -- --  ", "  ---   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "±": {"ch": "±", "code": 177, "map": ["        ", "        ", "        ", "        ", "        ", "   --   ", "   --   ", " ------ ", "   --   ", "   --   ", "        ", " ------ ", "        ", "        "]}, "²": {"ch": "²", "code": 178, "map": ["        ", "  ---   ", " -- --  ", "   --   ", "  --    ", " -----  ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "³": {"ch": "³", "code": 179, "map": ["        ", " ----   ", "    --  ", "  ---   ", "    --  ", " ----   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "´": {"ch": "´", "code": 180, "map": ["   --   ", "  --    ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "µ": {"ch": "µ", "code": 181, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--  --- ", "---- -- ", "--      ", "--      "]}, "¶": {"ch": "¶", "code": 182, "map": ["        ", "        ", " ------ ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " --- -- ", "   - -- ", "   - -- ", "   - -- ", "   - -- ", "        ", "        "]}, "·": {"ch": "·", "code": 183, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "   --   ", "   --   ", "        ", "        ", "        ", "        ", "        ", "        "]}, "¸": {"ch": "¸", "code": 184, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "  --    ", "  --    ", " --     "]}, "¹": {"ch": "¹", "code": 185, "map": ["        ", "   --   ", "  ---   ", "   --   ", "   --   ", "   --   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "º": {"ch": "º", "code": 186, "map": ["        ", "  ----  ", " --  -- ", " --  -- ", " --  -- ", "  ----  ", "        ", " ------ ", "        ", "        ", "        ", "        ", "        ", "        "]}, "»": {"ch": "»", "code": 187, "map": ["        ", "        ", "        ", "        ", "        ", "-- --   ", " -- --  ", "  -- -- ", "   -- --", "  -- -- ", " -- --  ", "-- --   ", "        ", "        "]}, "¼": {"ch": "¼", "code": 188, "map": [" --     ", "---     ", " --   - ", " --  -- ", " -- --  ", "   --   ", "  --    ", " --  -- ", "--  --- ", "-  -- - ", "  ----- ", "     -- ", "     -- ", "        "]}, "½": {"ch": "½", "code": 189, "map": [" --     ", "---     ", " --   - ", " --  -- ", " -- --  ", "   --   ", "  --    ", " --     ", "-- ---  ", "- -- -- ", "    --  ", "   --   ", "  ----- ", "        "]}, "¾": {"ch": "¾", "code": 190, "map": ["---     ", "  --    ", " --   - ", "  -- -- ", "--- --  ", "   --   ", "  --    ", " --  -- ", "--  --- ", "-  -- - ", "  ----- ", "     -- ", "     -- ", "        "]}, "¿": {"ch": "¿", "code": 191, "map": ["        ", "        ", "  --    ", "  --    ", "        ", "  --    ", "  --    ", " --     ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "À": {"ch": "À", "code": 192, "map": ["  --    ", "   --   ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Á": {"ch": "Á", "code": 193, "map": ["   --   ", "  --    ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Â": {"ch": "Â", "code": 194, "map": ["  ---   ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ã": {"ch": "Ã", "code": 195, "map": [" --- -- ", "-- ---  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ä": {"ch": "Ä", "code": 196, "map": [" -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Å": {"ch": "Å", "code": 197, "map": ["  ---   ", " -- --  ", "  ---   ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Æ": {"ch": "<PERSON>", "code": 198, "map": ["        ", "        ", " ------ ", "-- --   ", "-- --   ", "-- --   ", "------- ", "-- --   ", "-- --   ", "-- --   ", "-- --   ", "-- ---- ", "        ", "        "]}, "Ç": {"ch": "Ç", "code": 199, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "--      ", "--      ", "--      ", "--   -- ", "--   -- ", " -----  ", "  --    ", " --     "]}, "È": {"ch": "È", "code": 200, "map": ["  --    ", "   --   ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "É": {"ch": "É", "code": 201, "map": ["   --   ", "  --    ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "Ê": {"ch": "Ê", "code": 202, "map": ["  ---   ", " -- --  ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "Ë": {"ch": "Ë", "code": 203, "map": [" -- --  ", " -- --  ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "Ì": {"ch": "Ì", "code": 204, "map": ["  --    ", "   --   ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Í": {"ch": "Í", "code": 205, "map": ["    --  ", "   --   ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Î": {"ch": "Î", "code": 206, "map": ["  ---   ", " -- --  ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ï": {"ch": "Ï", "code": 207, "map": [" --  -- ", " --  -- ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ð": {"ch": "Ð", "code": 208, "map": ["        ", "        ", " ----   ", " -- --  ", " --  -- ", " --  -- ", "---- -- ", " --  -- ", " --  -- ", " --  -- ", " -- --  ", " ----   ", "        ", "        "]}, "Ñ": {"ch": "Ñ", "code": 209, "map": [" --- -- ", "-- ---  ", "        ", "--   -- ", "--   -- ", "---  -- ", "---- -- ", "-- ---- ", "--  --- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ò": {"ch": "Ò", "code": 210, "map": ["  --    ", "   --   ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ó": {"ch": "<PERSON>", "code": 211, "map": ["   --   ", "  --    ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ô": {"ch": "Ô", "code": 212, "map": ["  ---   ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Õ": {"ch": "Õ", "code": 213, "map": [" --- -- ", "-- ---  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ö": {"ch": "Ö", "code": 214, "map": [" -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "×": {"ch": "×", "code": 215, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", " -- --  ", "  ---   ", "  ---   ", " -- --  ", "--   -- ", "        ", "        ", "        "]}, "Ø": {"ch": "Ø", "code": 216, "map": ["        ", "        ", " -----  ", "--   ---", "--   -- ", "--  --- ", "-- ---- ", "---- -- ", "---  -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ù": {"ch": "Ù", "code": 217, "map": ["  --    ", "   --   ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ú": {"ch": "Ú", "code": 218, "map": ["   --   ", "  --    ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Û": {"ch": "Û", "code": 219, "map": ["  ---   ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ü": {"ch": "Ü", "code": 220, "map": [" -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ý": {"ch": "Ý", "code": 221, "map": ["    --  ", "   --   ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "Þ": {"ch": "Þ", "code": 222, "map": ["        ", "        ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--      ", "--      ", "        ", "        "]}, "ß": {"ch": "ß", "code": 223, "map": ["        ", "        ", " ----   ", "--  --  ", "--  --  ", "--  -   ", "------  ", "--   -- ", "--   -- ", "--   -- ", "---  -- ", "-- ---  ", "        ", "        "]}, "à": {"ch": "à", "code": 224, "map": ["        ", "        ", "  --    ", "   --   ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "á": {"ch": "á", "code": 225, "map": ["        ", "        ", "   --   ", "  --    ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "â": {"ch": "â", "code": 226, "map": ["        ", "        ", "  ---   ", " -- --  ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "ã": {"ch": "ã", "code": 227, "map": ["        ", "        ", " --- -- ", "-- ---  ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "ä": {"ch": "ä", "code": 228, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "å": {"ch": "å", "code": 229, "map": ["        ", "        ", "  ---   ", " -- --  ", "  ---   ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "æ": {"ch": "æ", "code": 230, "map": ["        ", "        ", "        ", "        ", "        ", " -- --  ", "   - -- ", "   - -- ", " ------ ", "-- -    ", "-- -    ", " -- --  ", "        ", "        "]}, "ç": {"ch": "ç", "code": 231, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", " -----  ", "  --    ", " --     "]}, "è": {"ch": "è", "code": 232, "map": ["        ", "        ", "  --    ", "   --   ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "é": {"ch": "é", "code": 233, "map": ["        ", "        ", "   --   ", "  --    ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "ê": {"ch": "ê", "code": 234, "map": ["        ", "        ", "  ---   ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "ë": {"ch": "ë", "code": 235, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "ì": {"ch": "ì", "code": 236, "map": ["        ", "        ", "  --    ", "   --   ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "í": {"ch": "í", "code": 237, "map": ["        ", "        ", "    --  ", "   --   ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "î": {"ch": "î", "code": 238, "map": ["        ", "        ", "  ---   ", " -- --  ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "ï": {"ch": "ï", "code": 239, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "ð": {"ch": "ð", "code": 240, "map": ["        ", "        ", " -- -   ", "  --    ", " - --   ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ñ": {"ch": "ñ", "code": 241, "map": ["        ", "        ", " --- -- ", "-- ---  ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ò": {"ch": "ò", "code": 242, "map": ["        ", "        ", "  --    ", "   --   ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ó": {"ch": "ó", "code": 243, "map": ["        ", "        ", "   --   ", "  --    ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ô": {"ch": "ô", "code": 244, "map": ["        ", "        ", "  ---   ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "õ": {"ch": "õ", "code": 245, "map": ["        ", "        ", " --- -- ", "-- ---  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ö": {"ch": "ö", "code": 246, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "÷": {"ch": "÷", "code": 247, "map": ["        ", "        ", "        ", "        ", "   --   ", "   --   ", "        ", " ------ ", "        ", "   --   ", "   --   ", "        ", "        ", "        "]}, "ø": {"ch": "ø", "code": 248, "map": ["        ", "        ", "        ", "        ", "        ", "  ---- -", " --  ---", " -- --- ", " ------ ", " --- -- ", "---  -- ", "- ----  ", "        ", "        "]}, "ù": {"ch": "ù", "code": 249, "map": ["        ", "        ", "  --    ", "   --   ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "ú": {"ch": "ú", "code": 250, "map": ["        ", "        ", "   --   ", "  --    ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "û": {"ch": "û", "code": 251, "map": ["        ", "        ", "  ---   ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "ü": {"ch": "ü", "code": 252, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "ý": {"ch": "ý", "code": 253, "map": ["        ", "        ", "   --   ", "  --    ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "þ": {"ch": "þ", "code": 254, "map": ["        ", "        ", "--      ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--      ", "--      "]}, "ÿ": {"ch": "ÿ", "code": 255, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "Ā": {"ch": "Ā", "code": 256, "map": [" -----  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ā": {"ch": "ā", "code": 257, "map": ["        ", "        ", "        ", " -----  ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ă": {"ch": "Ă", "code": 258, "map": [" -- --  ", "  ---   ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ă": {"ch": "ă", "code": 259, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ą": {"ch": "Ą", "code": 260, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "    --  ", "     ---"]}, "ą": {"ch": "ą", "code": 261, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "    --  ", "     ---"]}, "Ć": {"ch": "Ć", "code": 262, "map": ["   --   ", "  --    ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ć": {"ch": "ć", "code": 263, "map": ["        ", "        ", "   --   ", "  --    ", "        ", " -----  ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", " -----  ", "        ", "        "]}, "Ĉ": {"ch": "Ĉ", "code": 264, "map": ["  ---   ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ĉ": {"ch": "ĉ", "code": 265, "map": ["        ", "        ", "  ---   ", " -- --  ", "        ", " -----  ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", " -----  ", "        ", "        "]}, "Ċ": {"ch": "Ċ", "code": 266, "map": ["   --   ", "   --   ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ċ": {"ch": "ċ", "code": 267, "map": ["        ", "        ", "   --   ", "   --   ", "        ", " -----  ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", " -----  ", "        ", "        "]}, "Č": {"ch": "Č", "code": 268, "map": [" -- --  ", "  ---   ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "č": {"ch": "č", "code": 269, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", " -----  ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", " -----  ", "        ", "        "]}, "Ď": {"ch": "Ď", "code": 270, "map": [" -- --  ", "  ---   ", "        ", "-----   ", "--  --  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--  --  ", "-----   ", "        ", "        "]}, "ď": {"ch": "ď", "code": 271, "map": [" -- --  ", "  ---   ", "     -- ", "     -- ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Đ": {"ch": "Đ", "code": 272, "map": ["        ", "        ", " ----   ", " -- --  ", " --  -- ", " --  -- ", "---- -- ", " --  -- ", " --  -- ", " --  -- ", " -- --  ", " ----   ", "        ", "        "]}, "đ": {"ch": "đ", "code": 273, "map": ["        ", "        ", "     -- ", "   -----", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ē": {"ch": "Ē", "code": 274, "map": [" -----  ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "ē": {"ch": "ē", "code": 275, "map": ["        ", "        ", "        ", " -----  ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "Ĕ": {"ch": "Ĕ", "code": 276, "map": [" -- --  ", "  ---   ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "ĕ": {"ch": "ĕ", "code": 277, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "Ė": {"ch": "Ė", "code": 278, "map": ["   --   ", "   --   ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "ė": {"ch": "ė", "code": 279, "map": ["        ", "        ", "   --   ", "   --   ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "Ę": {"ch": "Ę", "code": 280, "map": ["        ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "--      ", "------- ", "    --  ", "     ---"]}, "ę": {"ch": "ę", "code": 281, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "   --   ", "    --- "]}, "Ě": {"ch": "Ě", "code": 282, "map": [" -- --  ", "  ---   ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "ě": {"ch": "ě", "code": 283, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "Ĝ": {"ch": "Ĝ", "code": 284, "map": ["  ---   ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "-- ---- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ĝ": {"ch": "ĝ", "code": 285, "map": ["        ", "        ", "  ---   ", " -- --  ", "        ", " ------ ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "Ğ": {"ch": "Ğ", "code": 286, "map": [" -- --  ", "  ---   ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "-- ---- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ğ": {"ch": "ğ", "code": 287, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", " ------ ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "Ġ": {"ch": "Ġ", "code": 288, "map": ["   --   ", "   --   ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "-- ---- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ġ": {"ch": "ġ", "code": 289, "map": ["        ", "        ", "   --   ", "   --   ", "        ", " ------ ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "Ģ": {"ch": "Ģ", "code": 290, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "--      ", "-- ---- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "  --    ", " --     "]}, "ģ": {"ch": "ģ", "code": 291, "map": ["        ", "    --  ", "   --   ", "   --   ", "        ", " ------ ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "Ĥ": {"ch": "Ĥ", "code": 292, "map": ["  ---   ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ĥ": {"ch": "ĥ", "code": 293, "map": ["   ---  ", "  -- -- ", "--      ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ħ": {"ch": "Ħ", "code": 294, "map": ["        ", "        ", " --  -- ", "--------", " --  -- ", " --  -- ", " ------ ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "        ", "        "]}, "ħ": {"ch": "ħ", "code": 295, "map": ["        ", "        ", " --     ", "-----   ", " --     ", " -----  ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "        ", "        "]}, "Ĩ": {"ch": "Ĩ", "code": 296, "map": [" --- -- ", "-- ---  ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "ĩ": {"ch": "ĩ", "code": 297, "map": ["        ", "        ", " --- -- ", "-- ---  ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ī": {"ch": "Ī", "code": 298, "map": [" ------ ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "ī": {"ch": "ī", "code": 299, "map": ["        ", "        ", "        ", " -----  ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ĭ": {"ch": "Ĭ", "code": 300, "map": [" -- --  ", "  ---   ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "ĭ": {"ch": "ĭ", "code": 301, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Į": {"ch": "Į", "code": 302, "map": ["        ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "   --   ", "    --- "]}, "į": {"ch": "į", "code": 303, "map": ["        ", "        ", "   --   ", "   --   ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "   --   ", "    --- "]}, "İ": {"ch": "İ", "code": 304, "map": ["   --   ", "   --   ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "ı": {"ch": "ı", "code": 305, "map": ["        ", "        ", "        ", "        ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ĳ": {"ch": "Ĳ", "code": 306, "map": ["        ", "        ", "--  ----", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "---- -- ", "---- -- ", "-- ---  ", "        ", "        "]}, "ĳ": {"ch": "ĳ", "code": 307, "map": ["        ", "        ", "--   -- ", "--   -- ", "        ", "--  --- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "---- -- ", "  -- -- ", "   ---  "]}, "Ĵ": {"ch": "Ĵ", "code": 308, "map": ["   ---  ", "  -- -- ", "        ", "   ---- ", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "--  --  ", "--  --  ", " ----   ", "        ", "        "]}, "ĵ": {"ch": "ĵ", "code": 309, "map": ["        ", "        ", "    --- ", "   -- --", "        ", "    --- ", "     -- ", "     -- ", "     -- ", "     -- ", "     -- ", " --  -- ", " --  -- ", "  ----  "]}, "Ķ": {"ch": "Ķ", "code": 310, "map": ["        ", "        ", "--   -- ", "--   -- ", "--  --  ", "-- --   ", "----    ", "----    ", "-- --   ", "--  --  ", "--   -- ", "---- -- ", "  --    ", " --     "]}, "ķ": {"ch": "ķ", "code": 311, "map": ["        ", "        ", "--      ", "--      ", "--      ", "--   -- ", "--  --  ", "-- --   ", "----    ", "-- --   ", "--  --  ", "---- -- ", "  --    ", " --     "]}, "ĸ": {"ch": "ĸ", "code": 312, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--  --  ", "-- --   ", "----    ", "-- --   ", "--  --  ", "--   -- ", "        ", "        "]}, "Ĺ": {"ch": "Ĺ", "code": 313, "map": [" --     ", "--      ", "        ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "ĺ": {"ch": "ĺ", "code": 314, "map": ["    --  ", "   --   ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ļ": {"ch": "Ļ", "code": 315, "map": ["        ", "        ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "------- ", "  --    ", " --     "]}, "ļ": {"ch": "ļ", "code": 316, "map": ["        ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "   --   ", "  --    "]}, "Ľ": {"ch": "Ľ", "code": 317, "map": [" -- --  ", "  ---   ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "ľ": {"ch": "ľ", "code": 318, "map": [" -- --  ", "  ---   ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ŀ": {"ch": "Ŀ", "code": 319, "map": ["        ", "        ", "--      ", "--      ", "--      ", "--      ", "--  --  ", "--  --  ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "ŀ": {"ch": "ŀ", "code": 320, "map": ["        ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   -- --", "   -- --", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ł": {"ch": "Ł", "code": 321, "map": ["        ", "        ", " --     ", " --     ", " --     ", " --     ", " ---    ", "---     ", " --     ", " --     ", " --     ", " -------", "        ", "        "]}, "ł": {"ch": "ł", "code": 322, "map": ["        ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   ---  ", "  ---   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ń": {"ch": "Ń", "code": 323, "map": ["   --   ", "  --    ", "--   -- ", "--   -- ", "--   -- ", "---  -- ", "---- -- ", "-- ---- ", "--  --- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ń": {"ch": "ń", "code": 324, "map": ["        ", "        ", "   --   ", "  --    ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ņ": {"ch": "Ņ", "code": 325, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "---  -- ", "---- -- ", "-- ---- ", "--  --- ", "--   -- ", "--   -- ", "---- -- ", "  --    ", " --     "]}, "ņ": {"ch": "ņ", "code": 326, "map": ["        ", "        ", "        ", "        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "---- -- ", "  --    ", " --     "]}, "Ň": {"ch": "Ň", "code": 327, "map": [" -- --  ", "  ---   ", "--   -- ", "--   -- ", "--   -- ", "---  -- ", "---- -- ", "-- ---- ", "--  --- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ň": {"ch": "ň", "code": 328, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ŉ": {"ch": "ŉ", "code": 329, "map": ["        ", " --     ", " --     ", "--      ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ŋ": {"ch": "Ŋ", "code": 330, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "---  -- ", "---- -- ", "-- ---- ", "--  --- ", "--   -- ", "--   -- ", "--   -- ", "     -- ", "   ---  "]}, "ŋ": {"ch": "ŋ", "code": 331, "map": ["        ", "        ", "        ", "        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "     -- ", "   ---  "]}, "Ō": {"ch": "Ō", "code": 332, "map": [" -----  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ō": {"ch": "<PERSON>", "code": 333, "map": ["        ", "        ", "        ", " -----  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ŏ": {"ch": "Ŏ", "code": 334, "map": [" -- --  ", "  ---   ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ŏ": {"ch": "ŏ", "code": 335, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ő": {"ch": "Ő", "code": 336, "map": ["  -- -- ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ő": {"ch": "ő", "code": 337, "map": ["        ", "        ", "  -- -- ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Œ": {"ch": "Œ", "code": 338, "map": ["        ", "        ", " ------ ", "-- --   ", "-- --   ", "-- --   ", "-- ---- ", "-- --   ", "-- --   ", "-- --   ", "-- --   ", " ------ ", "        ", "        "]}, "œ": {"ch": "œ", "code": 339, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "-- - -- ", "-- - -- ", "-- ---- ", "-- -    ", "-- -    ", " -----  ", "        ", "        "]}, "Ŕ": {"ch": "Ŕ", "code": 340, "map": ["   --   ", "  --    ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "------  ", "----    ", "-- --   ", "--  --  ", "--   -- ", "        ", "        "]}, "ŕ": {"ch": "ŕ", "code": 341, "map": ["        ", "        ", "   --   ", "  --    ", "        ", "-- ---- ", "----    ", "---     ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "Ŗ": {"ch": "Ŗ", "code": 342, "map": ["        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "----    ", "-- --   ", "--  --  ", "---- -- ", "  --    ", " --     "]}, "ŗ": {"ch": "ŗ", "code": 343, "map": ["        ", "        ", "        ", "        ", "        ", "-- ---- ", "----    ", "---     ", "--      ", "--      ", "--      ", "---     ", " --     ", "--      "]}, "Ř": {"ch": "Ř", "code": 344, "map": [" -- --  ", "  ---   ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "------  ", "----    ", "-- --   ", "--  --  ", "--   -- ", "        ", "        "]}, "ř": {"ch": "ř", "code": 345, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", "-- ---- ", "----    ", "---     ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "Ś": {"ch": "Ś", "code": 346, "map": ["   --   ", "  --    ", "        ", " -----  ", "--   -- ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "--   -- ", " -----  ", "        ", "        "]}, "ś": {"ch": "ś", "code": 347, "map": ["        ", "        ", "    --  ", "   --   ", "        ", " ------ ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "------  ", "        ", "        "]}, "Ŝ": {"ch": "Ŝ", "code": 348, "map": ["  ---   ", " -- --  ", "        ", " -----  ", "--   -- ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "--   -- ", " -----  ", "        ", "        "]}, "ŝ": {"ch": "ŝ", "code": 349, "map": ["        ", "        ", "  ---   ", " -- --  ", "        ", " ------ ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "------  ", "        ", "        "]}, "Ş": {"ch": "Ş", "code": 350, "map": ["        ", "        ", " -----  ", "--   -- ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "--   -- ", "--   -- ", " -----  ", "  --    ", " --     "]}, "ş": {"ch": "ş", "code": 351, "map": ["        ", "        ", "        ", "        ", "        ", " ------ ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "------  ", "  --    ", " --     "]}, "Š": {"ch": "Š", "code": 352, "map": [" -- --  ", "  ---   ", "        ", " -----  ", "--   -- ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "--   -- ", " -----  ", "        ", "        "]}, "š": {"ch": "š", "code": 353, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", " ------ ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "------  ", "        ", "        "]}, "Ţ": {"ch": "Ţ", "code": 354, "map": ["        ", "        ", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   ---  ", "    --  ", "   --   "]}, "ţ": {"ch": "ţ", "code": 355, "map": ["        ", "        ", "  --    ", "  --    ", "  --    ", "------  ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "   ---- ", "    --  ", "   --   "]}, "Ť": {"ch": "Ť", "code": 356, "map": [" -- --  ", "  ---   ", "        ", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "ť": {"ch": "ť", "code": 357, "map": [" -- --  ", "  ---   ", "        ", "  --    ", "  --    ", "------  ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "   ---- ", "        ", "        "]}, "Ŧ": {"ch": "Ŧ", "code": 358, "map": ["        ", "        ", "--------", "   --   ", "   --   ", "   --   ", " ------ ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "ŧ": {"ch": "ŧ", "code": 359, "map": ["        ", "        ", "  --    ", "  --    ", "  --    ", "------  ", "  --    ", " ----   ", "  --    ", "  --    ", "  --    ", "   ---- ", "        ", "        "]}, "Ũ": {"ch": "Ũ", "code": 360, "map": [" --- -- ", "-- ---  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ũ": {"ch": "ũ", "code": 361, "map": ["        ", "        ", " --- -- ", "-- ---  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ū": {"ch": "Ū", "code": 362, "map": [" -----  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ū": {"ch": "ū", "code": 363, "map": ["        ", "        ", "        ", " -----  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ŭ": {"ch": "Ŭ", "code": 364, "map": [" -- --  ", "  ---   ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ŭ": {"ch": "ŭ", "code": 365, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ů": {"ch": "Ů", "code": 366, "map": ["  ---   ", " -- --  ", "  ---   ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ů": {"ch": "ů", "code": 367, "map": ["        ", "        ", "  ---   ", " -- --  ", "  ---   ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ű": {"ch": "Ű", "code": 368, "map": ["  -- -- ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ű": {"ch": "ű", "code": 369, "map": ["        ", "        ", "  -- -- ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ų": {"ch": "Ų", "code": 370, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "   --   ", "    --- "]}, "ų": {"ch": "ų", "code": 371, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "    --  ", "     ---"]}, "Ŵ": {"ch": "Ŵ", "code": 372, "map": ["  ---   ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "-- - -- ", "------- ", "--- --- ", "--   -- ", "-     - ", "        ", "        "]}, "ŵ": {"ch": "ŵ", "code": 373, "map": ["        ", "        ", "  ---   ", " -- --  ", "        ", "--   -- ", "--   -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "        ", "        "]}, "Ÿ": {"ch": "Ÿ", "code": 376, "map": [" --  -- ", " --  -- ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "Ź": {"ch": "Ź", "code": 377, "map": ["   --   ", "  --    ", "        ", "------- ", "     -- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "--      ", "------- ", "        ", "        "]}, "ź": {"ch": "ź", "code": 378, "map": ["        ", "        ", "   --   ", "  --    ", "        ", "------- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "------- ", "        ", "        "]}, "Ż": {"ch": "Ż", "code": 379, "map": ["   --   ", "   --   ", "        ", "------- ", "     -- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "--      ", "------- ", "        ", "        "]}, "ż": {"ch": "ż", "code": 380, "map": ["        ", "        ", "   --   ", "   --   ", "        ", "------- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "------- ", "        ", "        "]}, "Ž": {"ch": "Ž", "code": 381, "map": [" -- --  ", "  ---   ", "        ", "------- ", "     -- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "--      ", "------- ", "        ", "        "]}, "ž": {"ch": "ž", "code": 382, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", "------- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "------- ", "        ", "        "]}, "ſ": {"ch": "ſ", "code": 383, "map": ["        ", "        ", "   ---- ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "        ", "        "]}, "Ɔ": {"ch": "Ɔ", "code": 390, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "     -- ", "     -- ", "     -- ", "     -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ǝ": {"ch": "Ǝ", "code": 398, "map": ["        ", "        ", "------- ", "     -- ", "     -- ", "     -- ", "  ----- ", "     -- ", "     -- ", "     -- ", "     -- ", "------- ", "        ", "        "]}, "Ə": {"ch": "Ə", "code": 399, "map": ["        ", "        ", " -----  ", "--   -- ", "     -- ", "     -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ɛ": {"ch": "Ɛ", "code": 400, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", " ----   ", "--      ", "--      ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ƒ": {"ch": "ƒ", "code": 402, "map": ["        ", "        ", "    --- ", "   -- --", "   --   ", "   --   ", " ------ ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "-- --   ", " ---    "]}, "Ɲ": {"ch": "Ɲ", "code": 413, "map": ["        ", "        ", " --  -- ", " --  -- ", " --  -- ", " --- -- ", " ------ ", " -- --- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --     ", "--      "]}, "ƞ": {"ch": "ƞ", "code": 414, "map": ["        ", "        ", "        ", "        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "     -- ", "     -- "]}, "Ʒ": {"ch": "Ʒ", "code": 439, "map": ["        ", "        ", "------- ", "     -- ", "    --  ", "   --   ", "  ----  ", "     -- ", "     -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ș": {"ch": "Ș", "code": 536, "map": ["        ", "        ", " -----  ", "--   -- ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "--   -- ", "--   -- ", " -----  ", "  --    ", " --     "]}, "ș": {"ch": "ș", "code": 537, "map": ["        ", "        ", "        ", "        ", "        ", " ------ ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "------  ", "  --    ", " --     "]}, "Ț": {"ch": "Ț", "code": 538, "map": ["        ", "        ", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   ---  ", "    --  ", "   --   "]}, "ț": {"ch": "ț", "code": 539, "map": ["        ", "        ", "  --    ", "  --    ", "  --    ", "------  ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "   ---- ", "     -- ", "    --  "]}, "Ȳ": {"ch": "Ȳ", "code": 562, "map": [" ------ ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "ȳ": {"ch": "ȳ", "code": 563, "map": ["        ", "        ", "        ", " -----  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "ȷ": {"ch": "ȷ", "code": 567, "map": ["        ", "        ", "        ", "        ", "        ", "    --- ", "     -- ", "     -- ", "     -- ", "     -- ", "     -- ", " --  -- ", " --  -- ", "  ----  "]}, "ɔ": {"ch": "ɔ", "code": 596, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "     -- ", "     -- ", "     -- ", "--   -- ", " -----  ", "        ", "        "]}, "ɘ": {"ch": "ɘ", "code": 600, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "     -- ", "     -- ", " -----  ", "        ", "        "]}, "ə": {"ch": "ə", "code": 601, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "     -- ", "     -- ", "------- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ɛ": {"ch": "ɛ", "code": 603, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--      ", " ----   ", "--      ", "--   -- ", " -----  ", "        ", "        "]}, "ɲ": {"ch": "ɲ", "code": 626, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --     ", "--      "]}, "ʒ": {"ch": "ʒ", "code": 658, "map": ["        ", "        ", "        ", "        ", "        ", "------- ", "     -- ", "    --  ", "   --   ", "  ----  ", "     -- ", "     -- ", "--   -- ", " -----  "]}, "ʻ": {"ch": "ʻ", "code": 699, "map": ["   --   ", "  --    ", "  --    ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "ʼ": {"ch": "ʼ", "code": 700, "map": ["   --   ", "   --   ", "  --    ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "ʽ": {"ch": "ʽ", "code": 701, "map": ["  --    ", "  --    ", "   --   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "ˆ": {"ch": "ˆ", "code": 710, "map": ["  ---   ", " -- --  ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "ˇ": {"ch": "ˇ", "code": 711, "map": [" -- --  ", "  ---   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "˘": {"ch": "˘", "code": 728, "map": [" -- --  ", "  ---   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "˙": {"ch": "˙", "code": 729, "map": ["   --   ", "   --   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "˛": {"ch": "˛", "code": 731, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "     -- ", "    --  ", "     ---"]}, "˜": {"ch": "˜", "code": 732, "map": [" --- -- ", "-- ---  ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "˝": {"ch": "˝", "code": 733, "map": ["  -- -- ", " -- --  ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "̀": {"ch": "̀", "code": 768, "map": ["  --    ", "   --   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "́": {"ch": "́", "code": 769, "map": ["   --   ", "  --    ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "̂": {"ch": "̂", "code": 770, "map": ["  ---   ", " -- --  ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "̃": {"ch": "̃", "code": 771, "map": [" --- -- ", "-- ---  ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "̆": {"ch": "̆", "code": 774, "map": [" -- --  ", "  ---   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "̌": {"ch": "̌", "code": 780, "map": [" -- --  ", "  ---   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "̩": {"ch": "̩", "code": 809, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "   --   ", "   --   "]}, "΄": {"ch": "΄", "code": 900, "map": [" --     ", "--      ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "΅": {"ch": "΅", "code": 901, "map": ["   --   ", "  --    ", "        ", " -- --  ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "Ά": {"ch": "Ά", "code": 902, "map": [" --     ", "--      ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "·": {"ch": "·", "code": 903, "map": ["        ", "        ", "        ", "        ", "        ", "   --   ", "   --   ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "Έ": {"ch": "Έ", "code": 904, "map": [" --     ", "--      ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "Ή": {"ch": "Ή", "code": 905, "map": [" --     ", "--      ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ί": {"ch": "Ί", "code": 906, "map": [" --     ", "--      ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ό": {"ch": "Ό", "code": 908, "map": [" --     ", "--      ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ύ": {"ch": "Ύ", "code": 910, "map": [" --     ", "--      ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "Ώ": {"ch": "Ώ", "code": 911, "map": [" --     ", "--      ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", "--- --- ", "        ", "        "]}, "ΐ": {"ch": "ΐ", "code": 912, "map": ["   --   ", "  --    ", "-- --   ", "-- --   ", "        ", " ---    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "   ---  ", "        ", "        "]}, "Α": {"ch": "Α", "code": 913, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Β": {"ch": "Β", "code": 914, "map": ["        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "        ", "        "]}, "Γ": {"ch": "Γ", "code": 915, "map": ["        ", "        ", "------- ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "Δ": {"ch": "Δ", "code": 916, "map": ["        ", "        ", "   -    ", "   -    ", "  ---   ", "  ---   ", " -- --  ", " -- --  ", " -- --  ", "--   -- ", "--   -- ", "------- ", "        ", "        "]}, "Ε": {"ch": "Ε", "code": 917, "map": ["        ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "Ζ": {"ch": "Ζ", "code": 918, "map": ["        ", "        ", "------- ", "     -- ", "     -- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "--      ", "------- ", "        ", "        "]}, "Η": {"ch": "Η", "code": 919, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Θ": {"ch": "Θ", "code": 920, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "-- - -- ", "-- - -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ι": {"ch": "Ι", "code": 921, "map": ["        ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Κ": {"ch": "Κ", "code": 922, "map": ["        ", "        ", "--   -- ", "--   -- ", "--  --  ", "-- --   ", "----    ", "----    ", "-- --   ", "--  --  ", "--   -- ", "--   -- ", "        ", "        "]}, "Λ": {"ch": "Λ", "code": 923, "map": ["        ", "        ", "   -    ", "   -    ", "  ---   ", "  ---   ", " -- --  ", " -- --  ", " -- --  ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Μ": {"ch": "Μ", "code": 924, "map": ["        ", "        ", "-     - ", "--   -- ", "--- --- ", "------- ", "-- - -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ν": {"ch": "Ν", "code": 925, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "---  -- ", "---- -- ", "-- ---- ", "--  --- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ξ": {"ch": "Ξ", "code": 926, "map": ["        ", "        ", "------- ", "        ", "        ", "        ", " -----  ", "        ", "        ", "        ", "        ", "------- ", "        ", "        "]}, "Ο": {"ch": "Ο", "code": 927, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Π": {"ch": "Π", "code": 928, "map": ["        ", "        ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ρ": {"ch": "Ρ", "code": 929, "map": ["        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "Σ": {"ch": "Σ", "code": 931, "map": ["        ", "        ", "------- ", "--      ", " --     ", "  --    ", "   --   ", "   --   ", "  --    ", " --     ", "--      ", "------- ", "        ", "        "]}, "Τ": {"ch": "Τ", "code": 932, "map": ["        ", "        ", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "Υ": {"ch": "Υ", "code": 933, "map": ["        ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "Φ": {"ch": "Φ", "code": 934, "map": ["        ", "        ", "   -    ", " -----  ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "   -    ", "        ", "        "]}, "Χ": {"ch": "Χ", "code": 935, "map": ["        ", "        ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", "  ---   ", "  ---   ", " -- --  ", " -- --  ", "--   -- ", "--   -- ", "        ", "        "]}, "Ψ": {"ch": "Ψ", "code": 936, "map": ["        ", "        ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "   -    ", "   -    ", "        ", "        "]}, "Ω": {"ch": "Ω", "code": 937, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", "--- --- ", "        ", "        "]}, "Ϊ": {"ch": "Ϊ", "code": 938, "map": [" --  -- ", " --  -- ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ϋ": {"ch": "Ϋ", "code": 939, "map": [" --  -- ", " --  -- ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "ά": {"ch": "ά", "code": 940, "map": ["        ", "        ", "   --   ", "  --    ", "        ", " ---- - ", "--  --- ", "--  --  ", "--  --  ", "--  --  ", "--  --- ", " ---- - ", "        ", "        "]}, "έ": {"ch": "έ", "code": 941, "map": ["        ", "        ", "   --   ", "  --    ", "        ", " -----  ", "--   -- ", "--      ", " ----   ", "--      ", "--   -- ", " -----  ", "        ", "        "]}, "ή": {"ch": "ή", "code": 942, "map": ["        ", "        ", "   --   ", "  --    ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "     -- ", "     -- "]}, "ί": {"ch": "ί", "code": 943, "map": ["        ", "        ", "   --   ", "  --    ", "        ", " ---    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "   ---  ", "        ", "        "]}, "ΰ": {"ch": "ΰ", "code": 944, "map": ["   --   ", "  --    ", "        ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "α": {"ch": "α", "code": 945, "map": ["        ", "        ", "        ", "        ", "        ", " ---- - ", "--  --- ", "--  --  ", "--  --  ", "--  --  ", "--  --- ", " ---- - ", "        ", "        "]}, "β": {"ch": "β", "code": 946, "map": ["        ", "        ", " ----   ", "--  --  ", "--  --  ", "--  -   ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--      ", "--      "]}, "γ": {"ch": "γ", "code": 947, "map": ["        ", "        ", "        ", "        ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "  ----  ", "   --   ", "   --   ", "   --   "]}, "δ": {"ch": "δ", "code": 948, "map": ["        ", "        ", " ------ ", "  --    ", "   --   ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ε": {"ch": "ε", "code": 949, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--      ", " ----   ", "--      ", "--   -- ", " -----  ", "        ", "        "]}, "ζ": {"ch": "ζ", "code": 950, "map": ["        ", "        ", "------- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "--      ", "--      ", "--      ", " -----  ", "     -- ", "    --  "]}, "η": {"ch": "η", "code": 951, "map": ["        ", "        ", "        ", "        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "     -- ", "     -- "]}, "θ": {"ch": "θ", "code": 952, "map": ["        ", "        ", "  ----  ", " --  -- ", " --  -- ", " --  -- ", " ------ ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "  ----  ", "        ", "        "]}, "ι": {"ch": "ι", "code": 953, "map": ["        ", "        ", "        ", "        ", "        ", " ---    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "   ---  ", "        ", "        "]}, "κ": {"ch": "κ", "code": 954, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--  --  ", "-- --   ", "----    ", "-- --   ", "--  --  ", "--   -- ", "        ", "        "]}, "λ": {"ch": "λ", "code": 955, "map": ["        ", "        ", "  --    ", "  --    ", "   --   ", "   --   ", "  ----  ", "  ----  ", " --  -- ", " --  -- ", "--    --", "--    --", "        ", "        "]}, "μ": {"ch": "μ", "code": 956, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--  --- ", "---- -- ", "--      ", "--      "]}, "ν": {"ch": "ν", "code": 957, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", "  ---   ", "  ---   ", "        ", "        "]}, "ξ": {"ch": "ξ", "code": 958, "map": ["        ", "        ", " ------ ", "--      ", "--      ", "--      ", " -----  ", "--      ", "--      ", "--      ", "--      ", " -----  ", "     -- ", "    --  "]}, "ο": {"ch": "ο", "code": 959, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "π": {"ch": "π", "code": 960, "map": ["        ", "        ", "        ", "        ", "        ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ρ": {"ch": "ρ", "code": 961, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--      ", "--      "]}, "ς": {"ch": "ς", "code": 962, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--      ", "--      ", "--      ", "--      ", " -----  ", "     -- ", "    --  "]}, "σ": {"ch": "σ", "code": 963, "map": ["        ", "        ", "        ", "        ", "        ", "  ------", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "  ----  ", "        ", "        "]}, "τ": {"ch": "τ", "code": 964, "map": ["        ", "        ", "        ", "        ", "        ", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "    --- ", "        ", "        "]}, "υ": {"ch": "υ", "code": 965, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "φ": {"ch": "φ", "code": 966, "map": ["        ", "        ", "        ", "        ", "        ", " -  --  ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "   -    ", "   -    "]}, "χ": {"ch": "χ", "code": 967, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", "  ---   ", " -- --  ", " -- --  ", "--   -- ", "--   -- "]}, "ψ": {"ch": "ψ", "code": 968, "map": ["        ", "        ", "        ", "        ", "        ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "   -    ", "   -    "]}, "ω": {"ch": "ω", "code": 969, "map": ["        ", "        ", "        ", "        ", "        ", " -   -  ", "--   -- ", "-- - -- ", "-- - -- ", "-- - -- ", "------- ", " -- --  ", "        ", "        "]}, "ϊ": {"ch": "ϊ", "code": 970, "map": ["        ", "        ", "-- --   ", "-- --   ", "        ", " ---    ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "   ---  ", "        ", "        "]}, "ϋ": {"ch": "ϋ", "code": 971, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ό": {"ch": "ό", "code": 972, "map": ["        ", "        ", "   --   ", "  --    ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ύ": {"ch": "ύ", "code": 973, "map": ["        ", "        ", "   --   ", "  --    ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ώ": {"ch": "ώ", "code": 974, "map": ["        ", "        ", "   --   ", "  --    ", "        ", " -   -  ", "--   -- ", "-- - -- ", "-- - -- ", "-- - -- ", "------- ", " -- --  ", "        ", "        "]}, "ϳ": {"ch": "ϳ", "code": 1011, "map": ["        ", "        ", "     -- ", "     -- ", "        ", "    --- ", "     -- ", "     -- ", "     -- ", "     -- ", "     -- ", " --  -- ", " --  -- ", "  ----  "]}, "ϴ": {"ch": "ϴ", "code": 1012, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ѐ": {"ch": "Ѐ", "code": 1024, "map": ["  --    ", "   --   ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "Ё": {"ch": "Ё", "code": 1025, "map": [" -- --  ", " -- --  ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "Ђ": {"ch": "Ђ", "code": 1026, "map": ["        ", "        ", "----    ", " --     ", " --     ", " -----  ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " -- --  ", "        ", "        "]}, "Ѓ": {"ch": "Ѓ", "code": 1027, "map": ["   --   ", "  --    ", "        ", "------- ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "Є": {"ch": "Є", "code": 1028, "map": ["        ", "        ", " -----  ", "--   -- ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "--   -- ", " -----  ", "        ", "        "]}, "Ѕ": {"ch": "Ѕ", "code": 1029, "map": ["        ", "        ", " -----  ", "--   -- ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "І": {"ch": "І", "code": 1030, "map": ["        ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ї": {"ch": "Ї", "code": 1031, "map": [" --  -- ", " --  -- ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "Ј": {"ch": "Ј", "code": 1032, "map": ["        ", "        ", "   ---- ", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "--  --  ", "--  --  ", " ----   ", "        ", "        "]}, "Љ": {"ch": "Љ", "code": 1033, "map": ["        ", "        ", "  --    ", " ---    ", "-- -    ", "-- ---  ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-  ---  ", "        ", "        "]}, "Њ": {"ch": "Њ", "code": 1034, "map": ["        ", "        ", "-- -    ", "-- -    ", "-- -    ", "-- ---  ", "---- -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- ---  ", "        ", "        "]}, "Ћ": {"ch": "Ћ", "code": 1035, "map": ["        ", "        ", "----    ", " --     ", " --     ", " -----  ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "        ", "        "]}, "Ќ": {"ch": "Ќ", "code": 1036, "map": ["   --   ", "  --    ", "--   -- ", "--   -- ", "--  --  ", "-- --   ", "----    ", "----    ", "-- --   ", "--  --  ", "--   -- ", "--   -- ", "        ", "        "]}, "Ѝ": {"ch": "Ѝ", "code": 1037, "map": ["  --    ", "   --   ", "--   -- ", "--   -- ", "--   -- ", "--  --- ", "-- ---- ", "---- -- ", "---  -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ў": {"ch": "Ў", "code": 1038, "map": [" -- --  ", "  ---   ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "     -- ", " -----  ", "        ", "        "]}, "Џ": {"ch": "Џ", "code": 1039, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "  ---   ", "  ---   "]}, "А": {"ch": "А", "code": 1040, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Б": {"ch": "Б", "code": 1041, "map": ["        ", "        ", "------  ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "        ", "        "]}, "В": {"ch": "В", "code": 1042, "map": ["        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "        ", "        "]}, "Г": {"ch": "Г", "code": 1043, "map": ["        ", "        ", "------- ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "Д": {"ch": "Д", "code": 1044, "map": ["        ", "        ", "  ----- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "--------", "--    --", "        "]}, "Е": {"ch": "Е", "code": 1045, "map": ["        ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "Ж": {"ch": "Ж", "code": 1046, "map": ["        ", "        ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "  ---   ", " -----  ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "        ", "        "]}, "З": {"ch": "З", "code": 1047, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "     -- ", "  ----  ", "     -- ", "     -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "И": {"ch": "И", "code": 1048, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--  --- ", "-- ---- ", "---- -- ", "---  -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Й": {"ch": "Й", "code": 1049, "map": [" -- --  ", "  ---   ", "--   -- ", "--   -- ", "--   -- ", "--  --- ", "-- ---- ", "---- -- ", "---  -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "К": {"ch": "К", "code": 1050, "map": ["        ", "        ", "--   -- ", "--   -- ", "--  --  ", "-- --   ", "----    ", "----    ", "-- --   ", "--  --  ", "--   -- ", "--   -- ", "        ", "        "]}, "Л": {"ch": "Л", "code": 1051, "map": ["        ", "        ", "   ---- ", "  -- -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "--   -- ", "        ", "        "]}, "М": {"ch": "М", "code": 1052, "map": ["        ", "        ", "-     - ", "--   -- ", "--- --- ", "------- ", "-- - -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Н": {"ch": "Н", "code": 1053, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "О": {"ch": "О", "code": 1054, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "П": {"ch": "П", "code": 1055, "map": ["        ", "        ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Р": {"ch": "Р", "code": 1056, "map": ["        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "С": {"ch": "С", "code": 1057, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "--      ", "--      ", "--      ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Т": {"ch": "Т", "code": 1058, "map": ["        ", "        ", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "У": {"ch": "У", "code": 1059, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "     -- ", " -----  ", "        ", "        "]}, "Ф": {"ch": "Ф", "code": 1060, "map": ["        ", "   -    ", " -----  ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "   -    ", "        "]}, "Х": {"ch": "Х", "code": 1061, "map": ["        ", "        ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", "  ---   ", "  ---   ", " -- --  ", " -- --  ", "--   -- ", "--   -- ", "        ", "        "]}, "Ц": {"ch": "Ц", "code": 1062, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -------", "      --", "      --"]}, "Ч": {"ch": "Ч", "code": 1063, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "     -- ", "     -- ", "        ", "        "]}, "Ш": {"ch": "Ш", "code": 1064, "map": ["        ", "        ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " ------ ", "        ", "        "]}, "Щ": {"ch": "Щ", "code": 1065, "map": ["        ", "        ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " -------", "      --", "      --"]}, "Ъ": {"ch": "Ъ", "code": 1066, "map": ["        ", "        ", "---     ", " --     ", " --     ", " -----  ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " -----  ", "        ", "        "]}, "Ы": {"ch": "Ы", "code": 1067, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "---  -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "---  -- ", "        ", "        "]}, "Ь": {"ch": "Ь", "code": 1068, "map": ["        ", "        ", " --     ", " --     ", " --     ", " -----  ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " -----  ", "        ", "        "]}, "Э": {"ch": "Э", "code": 1069, "map": ["        ", "        ", " -----  ", "--   -- ", "     -- ", "     -- ", "  ----- ", "     -- ", "     -- ", "     -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ю": {"ch": "Ю", "code": 1070, "map": ["        ", "        ", "--  --  ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "---- -- ", "-- - -- ", "-- - -- ", "-- - -- ", "--  --  ", "        ", "        "]}, "Я": {"ch": "Я", "code": 1071, "map": ["        ", "        ", " ------ ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "   ---- ", "  -- -- ", " --  -- ", "--   -- ", "        ", "        "]}, "а": {"ch": "а", "code": 1072, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "б": {"ch": "б", "code": 1073, "map": ["        ", "        ", " -----  ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "        ", "        "]}, "в": {"ch": "в", "code": 1074, "map": ["        ", "        ", " ----   ", "--  --  ", "--  --  ", "--  -   ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "        ", "        "]}, "г": {"ch": "г", "code": 1075, "map": ["        ", "        ", "        ", "        ", "        ", "------- ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "д": {"ch": "д", "code": 1076, "map": ["        ", "        ", "        ", "        ", "        ", " ------ ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "е": {"ch": "е", "code": 1077, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "ж": {"ch": "ж", "code": 1078, "map": ["        ", "        ", "        ", "        ", "        ", "-- - -- ", "-- - -- ", " -----  ", "  ---   ", " -----  ", "-- - -- ", "-- - -- ", "        ", "        "]}, "з": {"ch": "з", "code": 1079, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "     -- ", "  ----  ", "     -- ", "--   -- ", " -----  ", "        ", "        "]}, "и": {"ch": "и", "code": 1080, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "й": {"ch": "й", "code": 1081, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "к": {"ch": "к", "code": 1082, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--  --  ", "-- --   ", "----    ", "-- --   ", "--  --  ", "--   -- ", "        ", "        "]}, "л": {"ch": "л", "code": 1083, "map": ["        ", "        ", "        ", "        ", "        ", "  ----- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "--   -- ", "        ", "        "]}, "м": {"ch": "м", "code": 1084, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--- --- ", "------- ", "-- - -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "н": {"ch": "н", "code": 1085, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "о": {"ch": "о", "code": 1086, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "п": {"ch": "п", "code": 1087, "map": ["        ", "        ", "        ", "        ", "        ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "р": {"ch": "р", "code": 1088, "map": ["        ", "        ", "        ", "        ", "        ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------  ", "--      ", "--      "]}, "с": {"ch": "с", "code": 1089, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", " -----  ", "        ", "        "]}, "т": {"ch": "т", "code": 1090, "map": ["        ", "        ", "        ", "        ", "        ", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "у": {"ch": "у", "code": 1091, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "ф": {"ch": "ф", "code": 1092, "map": ["        ", "        ", "        ", "        ", "   -    ", " -----  ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "   -    ", "        "]}, "х": {"ch": "х", "code": 1093, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", " -- --  ", "  ---   ", " -- --  ", "--   -- ", "--   -- ", "        ", "        "]}, "ц": {"ch": "ц", "code": 1094, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -------", "      --", "      --"]}, "ч": {"ch": "ч", "code": 1095, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "     -- ", "        ", "        "]}, "ш": {"ch": "ш", "code": 1096, "map": ["        ", "        ", "        ", "        ", "        ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " ------ ", "        ", "        "]}, "щ": {"ch": "щ", "code": 1097, "map": ["        ", "        ", "        ", "        ", "        ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", " -------", "      --", "      --"]}, "ъ": {"ch": "ъ", "code": 1098, "map": ["        ", "        ", "        ", "        ", "        ", "---     ", " --     ", " -----  ", " --  -- ", " --  -- ", " --  -- ", " -----  ", "        ", "        "]}, "ы": {"ch": "ы", "code": 1099, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "---  -- ", "-- - -- ", "-- - -- ", "-- - -- ", "---  -- ", "        ", "        "]}, "ь": {"ch": "ь", "code": 1100, "map": ["        ", "        ", "        ", "        ", "        ", " --     ", " --     ", " -----  ", " --  -- ", " --  -- ", " --  -- ", " -----  ", "        ", "        "]}, "э": {"ch": "э", "code": 1101, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "     -- ", "  ----- ", "     -- ", "--   -- ", " -----  ", "        ", "        "]}, "ю": {"ch": "ю", "code": 1102, "map": ["        ", "        ", "        ", "        ", "        ", "--  --  ", "-- - -- ", "-- - -- ", "---- -- ", "-- - -- ", "-- - -- ", "--  --  ", "        ", "        "]}, "я": {"ch": "я", "code": 1103, "map": ["        ", "        ", "        ", "        ", "        ", " ------ ", "--   -- ", "--   -- ", " ------ ", "  -- -- ", " --  -- ", "--   -- ", "        ", "        "]}, "ѐ": {"ch": "ѐ", "code": 1104, "map": ["        ", "        ", "  --    ", "   --   ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "ё": {"ch": "ё", "code": 1105, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "ђ": {"ch": "ђ", "code": 1106, "map": ["        ", "        ", " --     ", "-----   ", " --     ", " -----  ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "     -- ", "   ---  "]}, "ѓ": {"ch": "ѓ", "code": 1107, "map": ["        ", "        ", "   --   ", "  --    ", "        ", "------- ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "є": {"ch": "є", "code": 1108, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--      ", "-----   ", "--      ", "--   -- ", " -----  ", "        ", "        "]}, "ѕ": {"ch": "ѕ", "code": 1109, "map": ["        ", "        ", "        ", "        ", "        ", " ------ ", "--      ", "--      ", " -----  ", "     -- ", "     -- ", "------  ", "        ", "        "]}, "і": {"ch": "і", "code": 1110, "map": ["        ", "        ", "   --   ", "   --   ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "ї": {"ch": "ї", "code": 1111, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "ј": {"ch": "ј", "code": 1112, "map": ["        ", "        ", "     -- ", "     -- ", "        ", "    --- ", "     -- ", "     -- ", "     -- ", "     -- ", "     -- ", " --  -- ", " --  -- ", "  ----  "]}, "љ": {"ch": "љ", "code": 1113, "map": ["        ", "        ", "        ", "        ", "        ", " ---    ", "-- -    ", "-- ---  ", "-- - -- ", "-- - -- ", "-- - -- ", "-  ---  ", "        ", "        "]}, "њ": {"ch": "њ", "code": 1114, "map": ["        ", "        ", "        ", "        ", "        ", "-- -    ", "-- -    ", "-- ---  ", "---- -- ", "-- - -- ", "-- - -- ", "-- ---  ", "        ", "        "]}, "ћ": {"ch": "ћ", "code": 1115, "map": ["        ", "        ", " --     ", "-----   ", " --     ", " -----  ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "        ", "        "]}, "ќ": {"ch": "ќ", "code": 1116, "map": ["        ", "        ", "   --   ", "  --    ", "        ", "--   -- ", "--  --  ", "-- --   ", "----    ", "-- --   ", "--  --  ", "--   -- ", "        ", "        "]}, "ѝ": {"ch": "ѝ", "code": 1117, "map": ["        ", "        ", "  --    ", "   --   ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "ў": {"ch": "ў", "code": 1118, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "џ": {"ch": "џ", "code": 1119, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "  ---   ", "  ---   "]}, "Ґ": {"ch": "Ґ", "code": 1168, "map": ["     -- ", "     -- ", "------- ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "ґ": {"ch": "ґ", "code": 1169, "map": ["        ", "        ", "        ", "     -- ", "     -- ", "------- ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "        ", "        "]}, "Ғ": {"ch": "Ғ", "code": 1170, "map": ["        ", "        ", " -------", " --     ", " --     ", " --     ", "------  ", " --     ", " --     ", " --     ", " --     ", " --     ", "        ", "        "]}, "ғ": {"ch": "ғ", "code": 1171, "map": ["        ", "        ", "        ", "        ", "        ", " -------", " --     ", " --     ", "------  ", " --     ", " --     ", " --     ", "        ", "        "]}, "Ҕ": {"ch": "Ҕ", "code": 1172, "map": ["        ", "        ", "------- ", "--      ", "--      ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "     -- ", "    --  "]}, "ҕ": {"ch": "ҕ", "code": 1173, "map": ["        ", "        ", "        ", "        ", "        ", "------- ", "--      ", "--      ", "-----   ", "--  --  ", "--  --  ", "--  --  ", "    --  ", "   --   "]}, "Җ": {"ch": "Җ", "code": 1174, "map": ["        ", "        ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "  ---   ", " -----  ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - ---", "      --", "      --"]}, "җ": {"ch": "җ", "code": 1175, "map": ["        ", "        ", "        ", "        ", "        ", "-- - -- ", "-- - -- ", " -----  ", "  ---   ", " -----  ", "-- - -- ", "-- - ---", "      --", "      --"]}, "Ҙ": {"ch": "Ҙ", "code": 1176, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "     -- ", "  ----  ", "     -- ", "     -- ", "--   -- ", "--   -- ", " -----  ", "  --    ", "  --    "]}, "ҙ": {"ch": "ҙ", "code": 1177, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "     -- ", "  ----  ", "     -- ", "--   -- ", " -----  ", "  --    ", "  --    "]}, "Қ": {"ch": "Қ", "code": 1178, "map": ["        ", "        ", "--   -- ", "--   -- ", "--  --  ", "-- --   ", "----    ", "----    ", "-- --   ", "--  --  ", "--   -- ", "--   ---", "      --", "      --"]}, "қ": {"ch": "қ", "code": 1179, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--  --  ", "-- --   ", "----    ", "-- --   ", "--  --  ", "--   ---", "      --", "      --"]}, "Ҝ": {"ch": "Ҝ", "code": 1180, "map": ["        ", "        ", "--   -- ", "--   -- ", "-- - -- ", "-- ---  ", "-----   ", "-----   ", "-- ---  ", "-- - -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ҝ": {"ch": "ҝ", "code": 1181, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "-- - -- ", "-- ---  ", "-----   ", "-- ---  ", "-- - -- ", "--   -- ", "        ", "        "]}, "Ҡ": {"ch": "Ҡ", "code": 1184, "map": ["        ", "        ", "---   --", "---   --", " --  -- ", " -- --  ", " ----   ", " ----   ", " -- --  ", " --  -- ", " --   --", " --   --", "        ", "        "]}, "ҡ": {"ch": "ҡ", "code": 1185, "map": ["        ", "        ", "        ", "        ", "        ", "---   --", " --  -- ", " -- --  ", " ----   ", " -- --  ", " --  -- ", " --   --", "        ", "        "]}, "Ң": {"ch": "Ң", "code": 1186, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   ---", "      --", "      --"]}, "ң": {"ch": "ң", "code": 1187, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   ---", "      --", "      --"]}, "Ҥ": {"ch": "Ҥ", "code": 1188, "map": ["        ", "        ", "--  ----", "--  --  ", "--  --  ", "--  --  ", "------  ", "--  --  ", "--  --  ", "--  --  ", "--  --  ", "--  --  ", "        ", "        "]}, "ҥ": {"ch": "ҥ", "code": 1189, "map": ["        ", "        ", "        ", "        ", "        ", "--  ----", "--  --  ", "--  --  ", "------  ", "--  --  ", "--  --  ", "--  --  ", "        ", "        "]}, "Ҫ": {"ch": "Ҫ", "code": 1194, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--      ", "--      ", "--      ", "--      ", "--   -- ", "--   -- ", " -----  ", "  --    ", "  --    "]}, "ҫ": {"ch": "ҫ", "code": 1195, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--      ", "--      ", "--      ", "--   -- ", " -----  ", "  --    ", "  --    "]}, "Ү": {"ch": "Ү", "code": 1198, "map": ["        ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "ү": {"ch": "ү", "code": 1199, "map": ["        ", "        ", "        ", "        ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "  ----  ", "   --   ", "   --   ", "   --   "]}, "Ұ": {"ch": "Ұ", "code": 1200, "map": ["        ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "   --   ", " ------ ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "ұ": {"ch": "ұ", "code": 1201, "map": ["        ", "        ", "        ", "        ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "  ----  ", "   --   ", " ------ ", "   --   "]}, "Ҳ": {"ch": "Ҳ", "code": 1202, "map": ["        ", "        ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", "  ---   ", "  ---   ", " -- --  ", " -- --  ", "--   -- ", "--   ---", "      --", "      --"]}, "ҳ": {"ch": "ҳ", "code": 1203, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", " -- --  ", "  ---   ", " -- --  ", "--   -- ", "--   ---", "      --", "      --"]}, "Ҷ": {"ch": "Ҷ", "code": 1206, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "     -- ", "     ---", "      --", "      --"]}, "ҷ": {"ch": "ҷ", "code": 1207, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "     ---", "      --", "      --"]}, "Ҹ": {"ch": "Ҹ", "code": 1208, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "-- - -- ", "-- - -- ", " ------ ", "   - -- ", "   - -- ", "     -- ", "     -- ", "        ", "        "]}, "ҹ": {"ch": "ҹ", "code": 1209, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "-- - -- ", "-- - -- ", " ------ ", "   - -- ", "     -- ", "     -- ", "        ", "        "]}, "Һ": {"ch": "Һ", "code": 1210, "map": ["        ", "        ", "--      ", "--      ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "һ": {"ch": "һ", "code": 1211, "map": ["        ", "        ", "        ", "        ", "        ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "Ӑ": {"ch": "Ӑ", "code": 1232, "map": [" -- --  ", "  ---   ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ӑ": {"ch": "ӑ", "code": 1233, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ӓ": {"ch": "Ӓ", "code": 1234, "map": [" -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ӓ": {"ch": "ӓ", "code": 1235, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", " -----  ", "     -- ", " ------ ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ӕ": {"ch": "Ӕ", "code": 1236, "map": ["        ", "        ", " ------ ", "-- --   ", "-- --   ", "-- --   ", "------- ", "-- --   ", "-- --   ", "-- --   ", "-- --   ", "-- ---- ", "        ", "        "]}, "ӕ": {"ch": "ӕ", "code": 1237, "map": ["        ", "        ", "        ", "        ", "        ", " -- --  ", "   - -- ", "   - -- ", " ------ ", "-- -    ", "-- -    ", " -- --  ", "        ", "        "]}, "Ӗ": {"ch": "Ӗ", "code": 1238, "map": [" -- --  ", "  ---   ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "ӗ": {"ch": "ӗ", "code": 1239, "map": ["        ", "        ", " -- --  ", "  ---   ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "Ә": {"ch": "Ә", "code": 1240, "map": ["        ", "        ", " -----  ", "--   -- ", "     -- ", "     -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ә": {"ch": "ә", "code": 1241, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "     -- ", "     -- ", "------- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ӛ": {"ch": "Ӛ", "code": 1242, "map": [" -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "     -- ", "     -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ӛ": {"ch": "ӛ", "code": 1243, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", " -----  ", "     -- ", "     -- ", "------- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ӝ": {"ch": "Ӝ", "code": 1244, "map": [" -- --  ", " -- --  ", "        ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "  ---   ", " -----  ", "-- - -- ", "-- - -- ", "-- - -- ", "        ", "        "]}, "ӝ": {"ch": "ӝ", "code": 1245, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", "-- - -- ", "-- - -- ", " -----  ", "  ---   ", " -----  ", "-- - -- ", "-- - -- ", "        ", "        "]}, "Ӟ": {"ch": "Ӟ", "code": 1246, "map": [" -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "     -- ", "  ----  ", "     -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ӟ": {"ch": "ӟ", "code": 1247, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "     -- ", "  ----  ", "     -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ӣ": {"ch": "Ӣ", "code": 1250, "map": [" -----  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--  --- ", "-- ---- ", "---- -- ", "---  -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ӣ": {"ch": "ӣ", "code": 1251, "map": ["        ", "        ", "        ", " -----  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ӥ": {"ch": "Ӥ", "code": 1252, "map": [" -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--  --- ", "-- ---- ", "---- -- ", "---  -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ӥ": {"ch": "ӥ", "code": 1253, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "        ", "        "]}, "Ӧ": {"ch": "Ӧ", "code": 1254, "map": [" -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ӧ": {"ch": "ӧ", "code": 1255, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ө": {"ch": "Ө", "code": 1256, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ө": {"ch": "ө", "code": 1257, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ӫ": {"ch": "Ӫ", "code": 1258, "map": [" -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "ӫ": {"ch": "ӫ", "code": 1259, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ӭ": {"ch": "Ӭ", "code": 1260, "map": [" -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "     -- ", "     -- ", "  ----- ", "     -- ", "     -- ", "--   -- ", " -----  ", "        ", "        "]}, "ӭ": {"ch": "ӭ", "code": 1261, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", " -----  ", "--   -- ", "     -- ", "  ----- ", "     -- ", "--   -- ", " -----  ", "        ", "        "]}, "Ӯ": {"ch": "Ӯ", "code": 1262, "map": [" -----  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "     -- ", " -----  ", "        ", "        "]}, "ӯ": {"ch": "ӯ", "code": 1263, "map": ["        ", "        ", "        ", " -----  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "Ӱ": {"ch": "Ӱ", "code": 1264, "map": [" -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "     -- ", " -----  ", "        ", "        "]}, "ӱ": {"ch": "ӱ", "code": 1265, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "Ӳ": {"ch": "Ӳ", "code": 1266, "map": ["  -- -- ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "     -- ", " -----  ", "        ", "        "]}, "ӳ": {"ch": "ӳ", "code": 1267, "map": ["        ", "        ", "  -- -- ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, "Ӵ": {"ch": "Ӵ", "code": 1268, "map": [" -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "     -- ", "     -- ", "        ", "        "]}, "ӵ": {"ch": "ӵ", "code": 1269, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", "     -- ", "     -- ", "        ", "        "]}, "Ӹ": {"ch": "Ӹ", "code": 1272, "map": [" -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "--   -- ", "---  -- ", "-- - -- ", "-- - -- ", "-- - -- ", "-- - -- ", "---  -- ", "        ", "        "]}, "ӹ": {"ch": "ӹ", "code": 1273, "map": ["        ", "        ", " -- --  ", " -- --  ", "        ", "--   -- ", "--   -- ", "---  -- ", "-- - -- ", "-- - -- ", "-- - -- ", "---  -- ", "        ", "        "]}, "Ḵ": {"ch": "Ḵ", "code": 7732, "map": ["        ", "        ", "--   -- ", "--   -- ", "--  --  ", "-- --   ", "----    ", "----    ", "-- --   ", "--  --  ", "--   -- ", "--   -- ", "        ", " -----  "]}, "ḵ": {"ch": "ḵ", "code": 7733, "map": ["        ", "        ", "--      ", "--      ", "--      ", "--   -- ", "--  --  ", "-- --   ", "----    ", "-- --   ", "--  --  ", "--   -- ", "        ", " -----  "]}, "Ẹ": {"ch": "Ẹ", "code": 7864, "map": ["        ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "--      ", "------- ", "   --   ", "   --   "]}, "ẹ": {"ch": "ẹ", "code": 7865, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "   --   ", "   --   "]}, "Ẽ": {"ch": "Ẽ", "code": 7868, "map": [" --- -- ", "-- ---  ", "        ", "------- ", "--      ", "--      ", "--      ", "-----   ", "--      ", "--      ", "--      ", "------- ", "        ", "        "]}, "ẽ": {"ch": "ẽ", "code": 7869, "map": ["        ", "        ", " --- -- ", "-- ---  ", "        ", " -----  ", "--   -- ", "--   -- ", "------- ", "--      ", "--      ", " -----  ", "        ", "        "]}, "Ị": {"ch": "Ị", "code": 7882, "map": ["        ", "        ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "   --   ", "   --   "]}, "ị": {"ch": "ị", "code": 7883, "map": ["        ", "        ", "   --   ", "   --   ", "        ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ----  ", "   --   ", "   --   "]}, "Ọ": {"ch": "Ọ", "code": 7884, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "   --   ", "   --   "]}, "ọ": {"ch": "ọ", "code": 7885, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "   --   ", "   --   "]}, "Ụ": {"ch": "Ụ", "code": 7908, "map": ["        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "   --   ", "   --   "]}, "ụ": {"ch": "ụ", "code": 7909, "map": ["        ", "        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "   --   ", "   --   "]}, "Ỹ": {"ch": "Ỹ", "code": 7928, "map": [" --- -- ", "-- ---  ", "        ", "--    --", "--    --", " --  -- ", " --  -- ", "  ----  ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "ỹ": {"ch": "ỹ", "code": 7929, "map": ["        ", "        ", " --- -- ", "-- ---  ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " ------ ", "     -- ", " -----  "]}, " ": {"ch": " ", "code": 8192, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, " ": {"ch": " ", "code": 8193, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, " ": {"ch": " ", "code": 8194, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, " ": {"ch": " ", "code": 8195, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, " ": {"ch": " ", "code": 8196, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, " ": {"ch": " ", "code": 8197, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, " ": {"ch": " ", "code": 8198, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, " ": {"ch": " ", "code": 8199, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, " ": {"ch": " ", "code": 8200, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, " ": {"ch": " ", "code": 8201, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, " ": {"ch": " ", "code": 8202, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "‐": {"ch": "‐", "code": 8208, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", " -----  ", "        ", "        ", "        ", "        ", "        ", "        "]}, "‑": {"ch": "‑", "code": 8209, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", " -----  ", "        ", "        ", "        ", "        ", "        ", "        "]}, "‒": {"ch": "‒", "code": 8210, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "------- ", "        ", "        ", "        ", "        ", "        ", "        "]}, "–": {"ch": "–", "code": 8211, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "------- ", "        ", "        ", "        ", "        ", "        ", "        "]}, "—": {"ch": "—", "code": 8212, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "------- ", "        ", "        ", "        ", "        ", "        ", "        "]}, "―": {"ch": "―", "code": 8213, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "------- ", "        ", "        ", "        ", "        ", "        ", "        "]}, "‖": {"ch": "‖", "code": 8214, "map": ["        ", "        ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", "        ", "        "]}, "‗": {"ch": "‗", "code": 8215, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "------- ", "        ", "------- "]}, "‘": {"ch": "‘", "code": 8216, "map": ["        ", "   --   ", "  --    ", "  --    ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "’": {"ch": "’", "code": 8217, "map": ["        ", "   --   ", "   --   ", "  --    ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "‚": {"ch": "‚", "code": 8218, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "   --   ", "   --   ", "  --    ", "        "]}, "‛": {"ch": "‛", "code": 8219, "map": ["        ", "  --    ", "  --    ", "   --   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "“": {"ch": "“", "code": 8220, "map": ["        ", " --  -- ", "--  --  ", "--  --  ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "”": {"ch": "”", "code": 8221, "map": ["        ", "  --  --", "  --  --", " --  -- ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "„": {"ch": "„", "code": 8222, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", " --  -- ", " --  -- ", "--  --  ", "        "]}, "‟": {"ch": "‟", "code": 8223, "map": ["        ", "--  --  ", "--  --  ", " --  -- ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "†": {"ch": "†", "code": 8224, "map": ["        ", "        ", "   --   ", "   --   ", " ------ ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "‡": {"ch": "‡", "code": 8225, "map": ["        ", "        ", "   --   ", "   --   ", " ------ ", "   --   ", "   --   ", "   --   ", "   --   ", " ------ ", "   --   ", "   --   ", "        ", "        "]}, "•": {"ch": "•", "code": 8226, "map": ["        ", "        ", "        ", "        ", "        ", "   --   ", "  ----  ", "  ----  ", "   --   ", "        ", "        ", "        ", "        ", "        "]}, "…": {"ch": "…", "code": 8230, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "-- -- --", "-- -- --", "        ", "        "]}, "‰": {"ch": "‰", "code": 8240, "map": ["        ", "        ", "--- --  ", "- - --  ", "-----   ", "   --   ", "  --    ", "  --    ", " --     ", " -------", "-- - - -", "-- -----", "        ", "        "]}, "′": {"ch": "′", "code": 8242, "map": ["        ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "″": {"ch": "″", "code": 8243, "map": ["        ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "‹": {"ch": "‹", "code": 8249, "map": ["        ", "        ", "        ", "        ", "        ", "    --  ", "   --   ", "  --    ", " --     ", "  --    ", "   --   ", "    --  ", "        ", "        "]}, "›": {"ch": "›", "code": 8250, "map": ["        ", "        ", "        ", "        ", "        ", " --     ", "  --    ", "   --   ", "    --  ", "   --   ", "  --    ", " --     ", "        ", "        "]}, "‼": {"ch": "‼", "code": 8252, "map": ["        ", "        ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "        ", " --  -- ", " --  -- ", "        ", "        "]}, "‾": {"ch": "‾", "code": 8254, "map": ["------- ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "ⁿ": {"ch": "ⁿ", "code": 8319, "map": ["        ", " ----   ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "₧": {"ch": "₧", "code": 8359, "map": ["        ", "        ", "-----   ", "--  --  ", "--  --  ", "--  --  ", "----- - ", "--   -- ", "--  ----", "--   -- ", "--   -- ", "--    --", "        ", "        "]}, "€": {"ch": "€", "code": 8364, "map": ["        ", "        ", "        ", "   ---- ", "  --  --", " --     ", "------  ", " --     ", "------  ", " --     ", "  --  --", "   ---- ", "        ", "        "]}, "₮": {"ch": "₮", "code": 8366, "map": ["        ", "        ", "--------", "   --   ", "   --   ", "   ---- ", " ----   ", "   ---- ", " ----   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "ℎ": {"ch": "ℎ", "code": 8462, "map": ["        ", "        ", "--      ", "--      ", "--      ", "------  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "ℏ": {"ch": "ℏ", "code": 8463, "map": ["        ", "        ", " --     ", "-----   ", " --     ", " -----  ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "        ", "        "]}, "№": {"ch": "№", "code": 8470, "map": ["        ", "        ", "-  - -- ", "-  - -- ", "-  - -- ", "-- -    ", "----    ", "----    ", "- --    ", "-  - -- ", "-  -    ", "-  - -- ", "        ", "        "]}, "™": {"ch": "™", "code": 8482, "map": ["        ", "        ", "----- --", " - - - -", " - - - -", " - -   -", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "Ω": {"ch": "Ω", "code": 8486, "map": ["        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", "--- --- ", "        ", "        "]}, "←": {"ch": "←", "code": 8592, "map": ["        ", "        ", "        ", "        ", "  -     ", " --     ", "------- ", "------- ", " --     ", "  -     ", "        ", "        ", "        ", "        "]}, "↑": {"ch": "↑", "code": 8593, "map": ["        ", "        ", "   --   ", "  ----  ", " ------ ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        "]}, "→": {"ch": "→", "code": 8594, "map": ["        ", "        ", "        ", "        ", "    -   ", "    --  ", "------- ", "------- ", "    --  ", "    -   ", "        ", "        ", "        ", "        "]}, "↓": {"ch": "↓", "code": 8595, "map": ["        ", "        ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", " ------ ", "  ----  ", "   --   ", "        ", "        "]}, "↔": {"ch": "↔", "code": 8596, "map": ["        ", "        ", "        ", "        ", "  -  -  ", " --  -- ", "--------", "--------", " --  -- ", "  -  -  ", "        ", "        ", "        ", "        "]}, "↕": {"ch": "↕", "code": 8597, "map": ["        ", "        ", "   --   ", "  ----  ", " ------ ", "   --   ", "   --   ", "   --   ", "   --   ", " ------ ", "  ----  ", "   --   ", "        ", "        "]}, "↨": {"ch": "↨", "code": 8616, "map": ["        ", "        ", "   --   ", "  ----  ", " ------ ", "   --   ", "   --   ", "   --   ", " ------ ", "  ----  ", "   --   ", " ------ ", "        ", "        "]}, "↵": {"ch": "↵", "code": 8629, "map": ["        ", "        ", "     -- ", "     -- ", "     -- ", "     -- ", "  -  -- ", " --  -- ", "------- ", "------- ", " --     ", "  -     ", "        ", "        "]}, "⇐": {"ch": "⇐", "code": 8656, "map": ["        ", "        ", "        ", "   -    ", "  ----- ", " ------ ", "---     ", " ------ ", "  ----- ", "   -    ", "        ", "        ", "        ", "        "]}, "⇑": {"ch": "⇑", "code": 8657, "map": ["        ", "        ", "   -    ", "  ---   ", " -----  ", "--- --- ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", "        ", "        "]}, "⇒": {"ch": "⇒", "code": 8658, "map": ["        ", "        ", "        ", "   -    ", "-----   ", "------  ", "    --- ", "------  ", "-----   ", "   -    ", "        ", "        ", "        ", "        "]}, "⇓": {"ch": "⇓", "code": 8659, "map": ["        ", "        ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", "--- --- ", " -----  ", "  ---   ", "   -    ", "        ", "        "]}, "⇔": {"ch": "⇔", "code": 8660, "map": ["        ", "        ", "        ", "  -  -  ", " ------ ", "--------", "--    --", "--------", " ------ ", "  -  -  ", "        ", "        ", "        ", "        "]}, "⇕": {"ch": "⇕", "code": 8661, "map": ["        ", "        ", "   -    ", "  ---   ", " -----  ", "--- --- ", " -- --  ", " -- --  ", "--- --- ", " -----  ", "  ---   ", "   -    ", "        ", "        "]}, "∃": {"ch": "∃", "code": 8707, "map": ["        ", "        ", "        ", "------- ", "     -- ", "     -- ", "     -- ", "------- ", "     -- ", "     -- ", "     -- ", "------- ", "        ", "        "]}, "∅": {"ch": "∅", "code": 8709, "map": ["        ", "        ", "     -- ", "    --  ", " -----  ", "--  --- ", "-- ---- ", "---- -- ", "---  -- ", " -----  ", " --     ", "--      ", "        ", "        "]}, "∆": {"ch": "∆", "code": 8710, "map": ["        ", "        ", "   -    ", "   -    ", "  ---   ", "  ---   ", " -- --  ", " -- --  ", " -- --  ", "--   -- ", "--   -- ", "------- ", "        ", "        "]}, "∈": {"ch": "∈", "code": 8712, "map": ["        ", "        ", "        ", "  ----- ", " --     ", "--      ", "--      ", "------- ", "--      ", "--      ", " --     ", "  ----- ", "        ", "        "]}, "∊": {"ch": "∊", "code": 8714, "map": ["        ", "        ", "        ", "        ", "  ----- ", " --     ", "--      ", "------- ", "--      ", " --     ", "  ----- ", "        ", "        ", "        "]}, "−": {"ch": "−", "code": 8722, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "------- ", "        ", "        ", "        ", "        ", "        ", "        "]}, "∙": {"ch": "∙", "code": 8729, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "  ---   ", "  ---   ", "  ---   ", "        ", "        ", "        ", "        ", "        "]}, "√": {"ch": "√", "code": 8730, "map": ["        ", "    --- ", "    --  ", "    --  ", "    --  ", "    --  ", "--  --  ", "--  --  ", "--  --  ", " -- --  ", "  ----  ", "   ---  ", "        ", "        "]}, "∞": {"ch": "∞", "code": 8734, "map": ["        ", "        ", "        ", "        ", "        ", " -----  ", "-- - -- ", "-- - -- ", "-- - -- ", " -----  ", "        ", "        ", "        ", "        "]}, "∟": {"ch": "∟", "code": 8735, "map": ["        ", "        ", "        ", "        ", "--      ", "--      ", "--      ", "--      ", "--      ", "------- ", "        ", "        ", "        ", "        "]}, "∧": {"ch": "∧", "code": 8743, "map": ["        ", "        ", "        ", "        ", "   -    ", "   -    ", "  ---   ", "  ---   ", " -- --  ", " -- --  ", "--   -- ", "--   -- ", "        ", "        "]}, "∨": {"ch": "∨", "code": 8744, "map": ["        ", "        ", "        ", "        ", "--   -- ", "--   -- ", " -- --  ", " -- --  ", "  ---   ", "  ---   ", "   -    ", "   -    ", "        ", "        "]}, "∩": {"ch": "∩", "code": 8745, "map": ["        ", "        ", "        ", "        ", " -----  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "        ", "        "]}, "∪": {"ch": "∪", "code": 8746, "map": ["        ", "        ", "        ", "        ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", " -----  ", "        ", "        "]}, "≈": {"ch": "≈", "code": 8776, "map": ["        ", "        ", "        ", "        ", "        ", " --- -- ", "-- ---  ", "        ", " --- -- ", "-- ---  ", "        ", "        ", "        ", "        "]}, "≠": {"ch": "≠", "code": 8800, "map": ["        ", "        ", "        ", "        ", "     -- ", "------- ", "   --   ", "  --    ", "------- ", "--      ", "        ", "        ", "        ", "        "]}, "≡": {"ch": "≡", "code": 8801, "map": ["        ", "        ", "        ", "        ", "------- ", "        ", "        ", "------- ", "        ", "        ", "------- ", "        ", "        ", "        "]}, "≤": {"ch": "≤", "code": 8804, "map": ["        ", "        ", "        ", "    --  ", "   --   ", "  --    ", " --     ", "  --    ", "   --   ", "    --  ", "        ", " ------ ", "        ", "        "]}, "≥": {"ch": "≥", "code": 8805, "map": ["        ", "        ", "        ", "  --    ", "   --   ", "    --  ", "     -- ", "    --  ", "   --   ", "  --    ", "        ", " ------ ", "        ", "        "]}, "⌀": {"ch": "⌀", "code": 8960, "map": ["        ", "        ", "     -- ", "    --  ", " -----  ", "--  --- ", "-- ---- ", "---- -- ", "---  -- ", " -----  ", " --     ", "--      ", "        ", "        "]}, "⌂": {"ch": "⌂", "code": 8962, "map": ["        ", "        ", "        ", "        ", "   -    ", "  ---   ", " -- --  ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "        ", "        "]}, "⌐": {"ch": "⌐", "code": 8976, "map": ["        ", "        ", "        ", "        ", "        ", "------- ", "--      ", "--      ", "--      ", "        ", "        ", "        ", "        ", "        "]}, "⌠": {"ch": "⌠", "code": 8992, "map": ["        ", "        ", "    --- ", "   -- --", "   -- --", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "⌡": {"ch": "⌡", "code": 8993, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "-- --   ", "-- --   ", " ---    ", "        ", "        "]}, "⎺": {"ch": "⎺", "code": 9146, "map": ["--------", "--------", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "⎻": {"ch": "⎻", "code": 9147, "map": ["        ", "        ", "        ", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "⎼": {"ch": "⎼", "code": 9148, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "--------", "--------", "        ", "        ", "        "]}, "⎽": {"ch": "⎽", "code": 9149, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "--------", "--------"]}, "␉": {"ch": "␉", "code": 9225, "map": ["--  --  ", "--  --  ", "------  ", "--  --  ", "--  --  ", "--  --  ", "        ", "  ------", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "        "]}, "␊": {"ch": "␊", "code": 9226, "map": ["--      ", "--      ", "--      ", "--      ", "--      ", "-----   ", "        ", "  ------", "  --    ", "  ----  ", "  --    ", "  --    ", "  --    ", "        "]}, "␋": {"ch": "␋", "code": 9227, "map": ["--  --  ", "--  --  ", "--  --  ", "--  --  ", " ----   ", "  --    ", "        ", "  ------", "    --  ", "    --  ", "    --  ", "    --  ", "    --  ", "        "]}, "␌": {"ch": "␌", "code": 9228, "map": ["------  ", "--      ", "----    ", "--      ", "--      ", "--      ", "        ", "  ------", "  --    ", "  ----  ", "  --    ", "  --    ", "  --    ", "        "]}, "␍": {"ch": "␍", "code": 9229, "map": [" ----   ", "--  --  ", "--      ", "--      ", "--  --  ", " ----   ", "        ", "  ----- ", "  --  --", "  --  --", "  ----- ", "  -- -- ", "  --  --", "        "]}, "␤": {"ch": "␤", "code": 9252, "map": ["--  --  ", "--- --  ", "------  ", "-- ---  ", "--  --  ", "--  --  ", "        ", "  --    ", "  --    ", "  --    ", "  --    ", "  --    ", "  ------", "        "]}, "─": {"ch": "─", "code": 9472, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "━": {"ch": "━", "code": 9473, "map": ["        ", "        ", "        ", "        ", "        ", "--------", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "│": {"ch": "│", "code": 9474, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┃": {"ch": "┃", "code": 9475, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┈": {"ch": "┈", "code": 9480, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "- - - - ", "- - - - ", "        ", "        ", "        ", "        ", "        ", "        "]}, "┉": {"ch": "┉", "code": 9481, "map": ["        ", "        ", "        ", "        ", "        ", "- - - - ", "- - - - ", "- - - - ", "        ", "        ", "        ", "        ", "        ", "        "]}, "┊": {"ch": "┊", "code": 9482, "map": ["   --   ", "   --   ", "   --   ", "        ", "   --   ", "   --   ", "        ", "   --   ", "   --   ", "   --   ", "        ", "   --   ", "   --   ", "        "]}, "┋": {"ch": "┋", "code": 9483, "map": ["  ---   ", "  ---   ", "  ---   ", "        ", "  ---   ", "  ---   ", "        ", "  ---   ", "  ---   ", "  ---   ", "        ", "  ---   ", "  ---   ", "        "]}, "┌": {"ch": "┌", "code": 9484, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "   -----", "   -----", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┍": {"ch": "┍", "code": 9485, "map": ["        ", "        ", "        ", "        ", "        ", "   -----", "   -----", "   -----", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┎": {"ch": "┎", "code": 9486, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "  ------", "  ------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┏": {"ch": "┏", "code": 9487, "map": ["        ", "        ", "        ", "        ", "        ", "  ------", "  ------", "  ------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┐": {"ch": "┐", "code": 9488, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "-----   ", "-----   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┑": {"ch": "┑", "code": 9489, "map": ["        ", "        ", "        ", "        ", "        ", "-----   ", "-----   ", "-----   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┒": {"ch": "┒", "code": 9490, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "-----   ", "-----   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┓": {"ch": "┓", "code": 9491, "map": ["        ", "        ", "        ", "        ", "        ", "-----   ", "-----   ", "-----   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "└": {"ch": "└", "code": 9492, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   -----", "   -----", "        ", "        ", "        ", "        ", "        ", "        "]}, "┕": {"ch": "┕", "code": 9493, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   -----", "   -----", "   -----", "        ", "        ", "        ", "        ", "        ", "        "]}, "┖": {"ch": "┖", "code": 9494, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ------", "  ------", "        ", "        ", "        ", "        ", "        ", "        "]}, "┗": {"ch": "┗", "code": 9495, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ------", "  ------", "  ------", "        ", "        ", "        ", "        ", "        ", "        "]}, "┘": {"ch": "┘", "code": 9496, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "-----   ", "-----   ", "        ", "        ", "        ", "        ", "        ", "        "]}, "┙": {"ch": "┙", "code": 9497, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "-----   ", "-----   ", "-----   ", "        ", "        ", "        ", "        ", "        ", "        "]}, "┚": {"ch": "┚", "code": 9498, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "-----   ", "-----   ", "        ", "        ", "        ", "        ", "        ", "        "]}, "┛": {"ch": "┛", "code": 9499, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "-----   ", "-----   ", "-----   ", "        ", "        ", "        ", "        ", "        ", "        "]}, "├": {"ch": "├", "code": 9500, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   -----", "   -----", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┝": {"ch": "┝", "code": 9501, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   -----", "   -----", "   -----", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┞": {"ch": "┞", "code": 9502, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ------", "  ------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┟": {"ch": "┟", "code": 9503, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ------", "  ------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┠": {"ch": "┠", "code": 9504, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ------", "  ------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┡": {"ch": "┡", "code": 9505, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ------", "  ------", "  ------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┢": {"ch": "┢", "code": 9506, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ------", "  ------", "  ------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┣": {"ch": "┣", "code": 9507, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ------", "  ------", "  ------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┤": {"ch": "┤", "code": 9508, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "-----   ", "-----   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┥": {"ch": "┥", "code": 9509, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "-----   ", "-----   ", "-----   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┦": {"ch": "┦", "code": 9510, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "-----   ", "-----   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┧": {"ch": "┧", "code": 9511, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "-----   ", "-----   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┨": {"ch": "┨", "code": 9512, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "-----   ", "-----   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┩": {"ch": "┩", "code": 9513, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "-----   ", "-----   ", "-----   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┪": {"ch": "┪", "code": 9514, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "-----   ", "-----   ", "-----   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┫": {"ch": "┫", "code": 9515, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "-----   ", "-----   ", "-----   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┬": {"ch": "┬", "code": 9516, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┭": {"ch": "┭", "code": 9517, "map": ["        ", "        ", "        ", "        ", "        ", "-----   ", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┮": {"ch": "┮", "code": 9518, "map": ["        ", "        ", "        ", "        ", "        ", "   -----", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┯": {"ch": "┯", "code": 9519, "map": ["        ", "        ", "        ", "        ", "        ", "--------", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┰": {"ch": "┰", "code": 9520, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┱": {"ch": "┱", "code": 9521, "map": ["        ", "        ", "        ", "        ", "        ", "-----   ", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┲": {"ch": "┲", "code": 9522, "map": ["        ", "        ", "        ", "        ", "        ", "  ------", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┳": {"ch": "┳", "code": 9523, "map": ["        ", "        ", "        ", "        ", "        ", "--------", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "┴": {"ch": "┴", "code": 9524, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "┵": {"ch": "┵", "code": 9525, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "-----   ", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "┶": {"ch": "┶", "code": 9526, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   -----", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "┷": {"ch": "┷", "code": 9527, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "--------", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "┸": {"ch": "┸", "code": 9528, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "┹": {"ch": "┹", "code": 9529, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "-----   ", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "┺": {"ch": "┺", "code": 9530, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ------", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "┻": {"ch": "┻", "code": 9531, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "--------", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "┼": {"ch": "┼", "code": 9532, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┽": {"ch": "┽", "code": 9533, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "-----   ", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┾": {"ch": "┾", "code": 9534, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   -----", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "┿": {"ch": "┿", "code": 9535, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "--------", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╀": {"ch": "╀", "code": 9536, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╁": {"ch": "╁", "code": 9537, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "╂": {"ch": "╂", "code": 9538, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "╃": {"ch": "╃", "code": 9539, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "-----   ", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╄": {"ch": "╄", "code": 9540, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ------", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╅": {"ch": "╅", "code": 9541, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "-----   ", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "╆": {"ch": "╆", "code": 9542, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   -----", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "╇": {"ch": "╇", "code": 9543, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "--------", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╈": {"ch": "╈", "code": 9544, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "--------", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "╉": {"ch": "╉", "code": 9545, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "-----   ", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "╊": {"ch": "╊", "code": 9546, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ------", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "╋": {"ch": "╋", "code": 9547, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "--------", "--------", "--------", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "═": {"ch": "═", "code": 9552, "map": ["        ", "        ", "        ", "        ", "--------", "--------", "        ", "--------", "--------", "        ", "        ", "        ", "        ", "        "]}, "║": {"ch": "║", "code": 9553, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╒": {"ch": "╒", "code": 9554, "map": ["        ", "        ", "        ", "        ", "   -----", "   -----", "   --   ", "   -----", "   -----", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╓": {"ch": "╓", "code": 9555, "map": ["        ", "        ", "        ", "        ", "        ", "        ", " -------", " -------", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╔": {"ch": "╔", "code": 9556, "map": ["        ", "        ", "        ", "        ", " -------", " -------", " --     ", " -- ----", " -- ----", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╕": {"ch": "╕", "code": 9557, "map": ["        ", "        ", "        ", "        ", "-----   ", "-----   ", "   --   ", "-----   ", "-----   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╖": {"ch": "╖", "code": 9558, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "------  ", "------  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╗": {"ch": "╗", "code": 9559, "map": ["        ", "        ", "        ", "        ", "------  ", "------  ", "    --  ", "--- --  ", "--- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╘": {"ch": "╘", "code": 9560, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   -----", "   -----", "   --   ", "   -----", "   -----", "        ", "        ", "        ", "        ", "        "]}, "╙": {"ch": "╙", "code": 9561, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -------", " -------", "        ", "        ", "        ", "        ", "        ", "        "]}, "╚": {"ch": "╚", "code": 9562, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- ----", " -- ----", " --     ", " -------", " -------", "        ", "        ", "        ", "        ", "        "]}, "╛": {"ch": "╛", "code": 9563, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "-----   ", "-----   ", "   --   ", "-----   ", "-----   ", "        ", "        ", "        ", "        ", "        "]}, "╜": {"ch": "╜", "code": 9564, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", "------  ", "------  ", "        ", "        ", "        ", "        ", "        ", "        "]}, "╝": {"ch": "╝", "code": 9565, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", "--- --  ", "--- --  ", "    --  ", "------  ", "------  ", "        ", "        ", "        ", "        ", "        "]}, "╞": {"ch": "╞", "code": 9566, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   -----", "   -----", "   --   ", "   -----", "   -----", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╟": {"ch": "╟", "code": 9567, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- ----", " -- ----", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╠": {"ch": "╠", "code": 9568, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- ----", " -- ----", " --     ", " -- ----", " -- ----", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╡": {"ch": "╡", "code": 9569, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "-----   ", "-----   ", "   --   ", "-----   ", "-----   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╢": {"ch": "╢", "code": 9570, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", "--- --  ", "--- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╣": {"ch": "╣", "code": 9571, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", "--- --  ", "--- --  ", "    --  ", "--- --  ", "--- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╤": {"ch": "╤", "code": 9572, "map": ["        ", "        ", "        ", "        ", "--------", "--------", "        ", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╥": {"ch": "╥", "code": 9573, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "--------", "--------", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╦": {"ch": "╦", "code": 9574, "map": ["        ", "        ", "        ", "        ", "--------", "--------", "        ", "--- ----", "--- ----", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╧": {"ch": "╧", "code": 9575, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "--------", "--------", "        ", "--------", "--------", "        ", "        ", "        ", "        ", "        "]}, "╨": {"ch": "╨", "code": 9576, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "╩": {"ch": "╩", "code": 9577, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", "--- ----", "--- ----", "        ", "--------", "--------", "        ", "        ", "        ", "        ", "        "]}, "╪": {"ch": "╪", "code": 9578, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "--------", "--------", "   --   ", "--------", "--------", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╫": {"ch": "╫", "code": 9579, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", "--------", "--------", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╬": {"ch": "╬", "code": 9580, "map": [" -- --  ", " -- --  ", " -- --  ", " -- --  ", "--- ----", "--- ----", "        ", "--- ----", "--- ----", " -- --  ", " -- --  ", " -- --  ", " -- --  ", " -- --  "]}, "╭": {"ch": "╭", "code": 9581, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "     ---", "    ----", "   ---  ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╮": {"ch": "╮", "code": 9582, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "---     ", "----    ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╯": {"ch": "╯", "code": 9583, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ---   ", "----    ", "---     ", "        ", "        ", "        ", "        ", "        ", "        "]}, "╰": {"ch": "╰", "code": 9584, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   ---  ", "    ----", "     ---", "        ", "        ", "        ", "        ", "        ", "        "]}, "╴": {"ch": "╴", "code": 9588, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "-----   ", "-----   ", "        ", "        ", "        ", "        ", "        ", "        "]}, "╵": {"ch": "╵", "code": 9589, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "        ", "        ", "        ", "        ", "        ", "        "]}, "╶": {"ch": "╶", "code": 9590, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "   -----", "   -----", "        ", "        ", "        ", "        ", "        ", "        "]}, "╷": {"ch": "╷", "code": 9591, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "╸": {"ch": "╸", "code": 9592, "map": ["        ", "        ", "        ", "        ", "        ", "-----   ", "-----   ", "-----   ", "        ", "        ", "        ", "        ", "        ", "        "]}, "╹": {"ch": "╹", "code": 9593, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "        ", "        ", "        ", "        ", "        ", "        "]}, "╺": {"ch": "╺", "code": 9594, "map": ["        ", "        ", "        ", "        ", "        ", "   -----", "   -----", "   -----", "        ", "        ", "        ", "        ", "        ", "        "]}, "╻": {"ch": "╻", "code": 9595, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "╼": {"ch": "╼", "code": 9596, "map": ["        ", "        ", "        ", "        ", "        ", "   -----", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "╽": {"ch": "╽", "code": 9597, "map": ["   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   "]}, "╾": {"ch": "╾", "code": 9598, "map": ["        ", "        ", "        ", "        ", "        ", "-----   ", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        "]}, "╿": {"ch": "╿", "code": 9599, "map": ["  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "  ---   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   ", "   --   "]}, "▀": {"ch": "▀", "code": 9600, "map": ["--------", "--------", "--------", "--------", "--------", "--------", "--------", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "▁": {"ch": "▁", "code": 9601, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "--------", "--------"]}, "▂": {"ch": "▂", "code": 9602, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "--------", "--------", "--------", "--------"]}, "▃": {"ch": "▃", "code": 9603, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "--------", "--------", "--------", "--------", "--------"]}, "▄": {"ch": "▄", "code": 9604, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "--------", "--------", "--------", "--------", "--------", "--------", "--------"]}, "▅": {"ch": "▅", "code": 9605, "map": ["        ", "        ", "        ", "        ", "        ", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------"]}, "▆": {"ch": "▆", "code": 9606, "map": ["        ", "        ", "        ", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------"]}, "▇": {"ch": "▇", "code": 9607, "map": ["        ", "        ", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------"]}, "█": {"ch": "█", "code": 9608, "map": ["--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------", "--------"]}, "▉": {"ch": "▉", "code": 9609, "map": ["------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- "]}, "▊": {"ch": "▊", "code": 9610, "map": ["------  ", "------  ", "------  ", "------  ", "------  ", "------  ", "------  ", "------  ", "------  ", "------  ", "------  ", "------  ", "------  ", "------  "]}, "▋": {"ch": "▋", "code": 9611, "map": ["-----   ", "-----   ", "-----   ", "-----   ", "-----   ", "-----   ", "-----   ", "-----   ", "-----   ", "-----   ", "-----   ", "-----   ", "-----   ", "-----   "]}, "▌": {"ch": "▌", "code": 9612, "map": ["----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    "]}, "▍": {"ch": "▍", "code": 9613, "map": ["---     ", "---     ", "---     ", "---     ", "---     ", "---     ", "---     ", "---     ", "---     ", "---     ", "---     ", "---     ", "---     ", "---     "]}, "▎": {"ch": "▎", "code": 9614, "map": ["--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      ", "--      "]}, "▏": {"ch": "▏", "code": 9615, "map": ["-       ", "-       ", "-       ", "-       ", "-       ", "-       ", "-       ", "-       ", "-       ", "-       ", "-       ", "-       ", "-       ", "-       "]}, "▐": {"ch": "▐", "code": 9616, "map": ["    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----"]}, "░": {"ch": "░", "code": 9617, "map": ["-   -   ", "  -   - ", "-   -   ", "  -   - ", "-   -   ", "  -   - ", "-   -   ", "  -   - ", "-   -   ", "  -   - ", "-   -   ", "  -   - ", "-   -   ", "  -   - "]}, "▒": {"ch": "▒", "code": 9618, "map": ["- - - - ", " - - - -", "- - - - ", " - - - -", "- - - - ", " - - - -", "- - - - ", " - - - -", "- - - - ", " - - - -", "- - - - ", " - - - -", "- - - - ", " - - - -"]}, "▓": {"ch": "▓", "code": 9619, "map": ["--- --- ", "- --- --", "--- --- ", "- --- --", "--- --- ", "- --- --", "--- --- ", "- --- --", "--- --- ", "- --- --", "--- --- ", "- --- --", "--- --- ", "- --- --"]}, "▖": {"ch": "▖", "code": 9622, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    "]}, "▗": {"ch": "▗", "code": 9623, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----"]}, "▘": {"ch": "▘", "code": 9624, "map": ["----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "▙": {"ch": "▙", "code": 9625, "map": ["----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "--------", "--------", "--------", "--------", "--------", "--------", "--------"]}, "▚": {"ch": "▚", "code": 9626, "map": ["----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----"]}, "▛": {"ch": "▛", "code": 9627, "map": ["--------", "--------", "--------", "--------", "--------", "--------", "--------", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    "]}, "▜": {"ch": "▜", "code": 9628, "map": ["--------", "--------", "--------", "--------", "--------", "--------", "--------", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----"]}, "▝": {"ch": "▝", "code": 9629, "map": ["    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "        ", "        ", "        ", "        ", "        ", "        ", "        "]}, "▞": {"ch": "▞", "code": 9630, "map": ["    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "----    ", "----    ", "----    ", "----    ", "----    ", "----    ", "----    "]}, "▟": {"ch": "▟", "code": 9631, "map": ["    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "    ----", "--------", "--------", "--------", "--------", "--------", "--------", "--------"]}, "■": {"ch": "■", "code": 9632, "map": ["        ", "        ", "        ", "        ", " -----  ", " -----  ", " -----  ", " -----  ", " -----  ", " -----  ", "        ", "        ", "        ", "        "]}, "▬": {"ch": "▬", "code": 9644, "map": ["        ", "        ", "        ", "        ", "        ", "        ", "        ", "        ", "------- ", "------- ", "------- ", "------- ", "        ", "        "]}, "▮": {"ch": "▮", "code": 9646, "map": ["        ", "        ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "------- ", "        ", "        "]}, "▲": {"ch": "▲", "code": 9650, "map": ["        ", "        ", "        ", "   --   ", "   --   ", "  ----  ", "  ----  ", " ------ ", " ------ ", "--------", "--------", "        ", "        ", "        "]}, "▶": {"ch": "▶", "code": 9654, "map": ["        ", "        ", "        ", "--      ", "----    ", "------  ", "--------", "--------", "------  ", "----    ", "--      ", "        ", "        ", "        "]}, "▼": {"ch": "▼", "code": 9660, "map": ["        ", "        ", "        ", "--------", "--------", " ------ ", " ------ ", "  ----  ", "  ----  ", "   --   ", "   --   ", "        ", "        ", "        "]}, "◀": {"ch": "◀", "code": 9664, "map": ["        ", "        ", "        ", "      --", "    ----", "  ------", "--------", "--------", "  ------", "    ----", "      --", "        ", "        ", "        "]}, "◆": {"ch": "◆", "code": 9670, "map": ["        ", "        ", "        ", "        ", "   --   ", "  ----  ", " ------ ", "--------", " ------ ", "  ----  ", "   --   ", "        ", "        ", "        "]}, "◊": {"ch": "◊", "code": 9674, "map": ["        ", "        ", "        ", "        ", "   --   ", "  ----  ", " --  -- ", "--    --", " --  -- ", "  ----  ", "   --   ", "        ", "        ", "        "]}, "○": {"ch": "○", "code": 9675, "map": ["        ", "        ", "        ", "        ", "  ----  ", " --  -- ", " -    - ", " -    - ", " --  -- ", "  ----  ", "        ", "        ", "        ", "        "]}, "●": {"ch": "●", "code": 9679, "map": ["        ", "        ", "        ", "        ", "  ----  ", " ------ ", " ------ ", " ------ ", " ------ ", "  ----  ", "        ", "        ", "        ", "        "]}, "◘": {"ch": "◘", "code": 9688, "map": ["--------", "--------", "--------", "--------", "--------", "---  ---", "--    --", "--    --", "---  ---", "--------", "--------", "--------", "--------", "--------"]}, "◙": {"ch": "◙", "code": 9689, "map": ["--------", "--------", "--------", "--------", "--    --", "-  --  -", "- ---- -", "- ---- -", "-  --  -", "--    --", "--------", "--------", "--------", "--------"]}, "☺": {"ch": "☺", "code": 9786, "map": ["        ", "        ", " -----  ", "-     - ", "- - - - ", "-     - ", "-     - ", "- --- - ", "-  -  - ", "-     - ", "-     - ", " -----  ", "        ", "        "]}, "☻": {"ch": "☻", "code": 9787, "map": ["        ", "        ", " -----  ", "------- ", "-- - -- ", "------- ", "------- ", "--   -- ", "--- --- ", "------- ", "------- ", " -----  ", "        ", "        "]}, "☼": {"ch": "☼", "code": 9788, "map": ["        ", "        ", "        ", "   --   ", "-- -- --", " ------ ", "  ----  ", "---  ---", "  ----  ", " ------ ", "-- -- --", "   --   ", "        ", "        "]}, "♀": {"ch": "♀", "code": 9792, "map": ["        ", "        ", "  ----  ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "  ----  ", "   --   ", " ------ ", "   --   ", "   --   ", "        ", "        "]}, "♂": {"ch": "♂", "code": 9794, "map": ["        ", "        ", "  ----- ", "    --- ", "   -- - ", "  --  - ", " ----   ", "--  --  ", "--  --  ", "--  --  ", "--  --  ", " ----   ", "        ", "        "]}, "♠": {"ch": "♠", "code": 9824, "map": ["        ", "        ", "   --   ", "   --   ", "  ----  ", " ------ ", "--------", "--------", " ------ ", "   --   ", "   --   ", "  ----  ", "        ", "        "]}, "♣": {"ch": "♣", "code": 9827, "map": ["        ", "        ", "   --   ", "  ----  ", "  ----  ", "   --   ", " - -- - ", "--------", "--------", " - -- - ", "   --   ", "  ----  ", "        ", "        "]}, "♥": {"ch": "♥", "code": 9829, "map": ["        ", "        ", "        ", " -- --  ", "------- ", "------- ", "------- ", "------- ", " -----  ", "  ---   ", "   -    ", "        ", "        ", "        "]}, "♦": {"ch": "♦", "code": 9830, "map": ["        ", "        ", "        ", "        ", "   --   ", "  ----  ", " ------ ", "--------", " ------ ", "  ----  ", "   --   ", "        ", "        ", "        "]}, "♪": {"ch": "♪", "code": 9834, "map": ["        ", "        ", " ------ ", " --  -- ", " ------ ", " --     ", " --     ", " --     ", " --     ", " --     ", "---     ", "--      ", "        ", "        "]}, "♫": {"ch": "♫", "code": 9835, "map": ["        ", "        ", " ------ ", " --  -- ", " ------ ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", " -- --- ", "--- --  ", "--      ", "        "]}, "✓": {"ch": "✓", "code": 10003, "map": ["        ", "        ", "      --", "      --", "     -- ", "     -- ", "--  --  ", "--  --  ", " ----   ", " ----   ", "  --    ", "  --    ", "        ", "        "]}, "✔": {"ch": "✔", "code": 10004, "map": ["        ", "        ", "     ---", "     ---", "    --- ", "    --- ", "-- ---  ", "------  ", " ----   ", " ----   ", "  --    ", "  --    ", "        ", "        "]}, "✗": {"ch": "✗", "code": 10007, "map": ["        ", "        ", "    --  ", "--  --  ", " ----   ", "  ---   ", "  ---   ", "  ----  ", " --  -- ", " --     ", "--      ", "--      ", "        ", "        "]}, "✘": {"ch": "✘", "code": 10008, "map": ["        ", "        ", "    --- ", "--- --- ", " -----  ", "  ----  ", "  ----  ", "  ----- ", " --- ---", " ---  --", "---     ", "---     ", "        ", "        "]}, "": {"ch": "", "code": 57504, "map": ["--      ", "--  --  ", "-- ---- ", "--------", "--  --  ", "--  --  ", "--  --  ", "-  --   ", "  --    ", " --     ", "--      ", "--      ", "--      ", "--      "]}, "": {"ch": "", "code": 57505, "map": ["--      ", "--      ", "--      ", "--      ", "--      ", "------  ", "        ", "  --  --", "  --- --", "  ------", "  -- ---", "  --  --", "  --  --", "        "]}, "": {"ch": "", "code": 57506, "map": ["  ----  ", " --  -- ", " --  -- ", " --  -- ", " --  -- ", "--------", "--------", "---  ---", "--    --", "---  ---", "--------", "--------", "--------", "        "]}, "": {"ch": "", "code": 57520, "map": ["-       ", "--      ", "---     ", "----    ", "-----   ", "------  ", "------- ", "------- ", "------  ", "-----   ", "----    ", "---     ", "--      ", "-       "]}, "": {"ch": "", "code": 57521, "map": ["-       ", "--      ", " --     ", "  --    ", "   --   ", "    --  ", "     -- ", "     -- ", "    --  ", "   --   ", "  --    ", " --     ", "--      ", "-       "]}, "": {"ch": "", "code": 57522, "map": ["       -", "      --", "     ---", "    ----", "   -----", "  ------", " -------", " -------", "  ------", "   -----", "    ----", "     ---", "      --", "       -"]}, "": {"ch": "", "code": 57523, "map": ["       -", "      --", "     -- ", "    --  ", "   --   ", "  --    ", " --     ", " --     ", "  --    ", "   --   ", "    --  ", "     -- ", "      --", "       -"]}, "": {"ch": "", "code": 63166, "map": ["        ", "        ", "        ", "        ", "        ", "    --- ", "     -- ", "     -- ", "     -- ", "     -- ", "     -- ", " --  -- ", " --  -- ", "  ----  "]}, "�": {"ch": "�", "code": 65533, "map": ["        ", "        ", "------- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "--   -- ", "------- ", "        ", "        "]}}}