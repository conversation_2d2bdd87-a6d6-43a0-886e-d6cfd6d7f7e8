# Installation
> `npm install --save @types/inquirer`

# Summary
This package contains type definitions for inquirer (https://github.com/SBoudrias/Inquirer.js).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/inquirer.

### Additional Details
 * Last updated: Mon, 05 May 2025 19:02:21 GMT
 * Dependencies: [@types/through](https://npmjs.com/package/@types/through), [rxjs](https://npmjs.com/package/rxjs)

# Credits
These definitions were written by [<PERSON><PERSON><PERSON>](https://github.com/tkQubo), [<PERSON><PERSON><PERSON>](https://github.com/ppathan), [<PERSON><PERSON><PERSON>](https://github.com/jouderianjr), [<PERSON><PERSON>](https://github.com/bang88), [<PERSON>](https://github.com/bitjson), [Synarque](https://github.com/synarque), [<PERSON>](https://github.com/jrockwood), [<PERSON>](https://github.com/kwkelly), and [<PERSON>](https://github.com/chigix).
