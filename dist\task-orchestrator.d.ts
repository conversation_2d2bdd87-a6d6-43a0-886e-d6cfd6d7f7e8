import { Task } from './types';
import { WorkerAI } from './worker-ai';
import { Logger } from './logger';
export interface TaskPriority {
    level: 'low' | 'normal' | 'high' | 'urgent';
    weight: number;
}
export interface WorkerCapability {
    name: string;
    proficiency: number;
    currentLoad: number;
    averageResponseTime: number;
    successRate: number;
}
export interface TaskAnalysis {
    complexity: number;
    estimatedTime: number;
    requiredCapabilities: string[];
    priority: TaskPriority;
    canBeSplit: boolean;
    dependencies: string[];
}
export declare class TaskOrchestrator {
    private workers;
    private taskQueue;
    private activeTasksPerWorker;
    private workerCapabilities;
    private taskHistory;
    private logger;
    constructor(logger: Logger);
    registerWorker(worker: WorkerAI): void;
    private initializeWorkerCapabilities;
    private calculateInitialProficiency;
    analyzeTask(description: string, context?: string): Promise<TaskAnalysis>;
    private calculateComplexity;
    private estimateTaskTime;
    private identifyRequiredCapabilities;
    private determinePriority;
    private canTaskBeSplit;
    findOptimalWorker(taskAnalysis: TaskAnalysis): Promise<WorkerAI | null>;
    private calculateWorkerScore;
    distributeTask(description: string, context?: string): Promise<Task | null>;
    updateWorkerPerformance(workerId: string, task: Task, responseTime: number, success: boolean): void;
    getWorkerStatistics(workerId: string): any;
    getAllStatistics(): any;
}
//# sourceMappingURL=task-orchestrator.d.ts.map